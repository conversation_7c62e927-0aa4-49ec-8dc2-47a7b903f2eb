<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class PickClass extends CommonClass
{
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}

	public function PickConfigurationSave($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Parse configuration details if provided
			$configurationDetails = [];
			if (isset($data['ConfigurationDetails']) && is_array($data['ConfigurationDetails'])) {
				$configurationDetails = $data['ConfigurationDetails'];
			}

			if ($data['ConfigurationID'] == '' || !isset($data['ConfigurationID'])) { // If New Configuration
				// Check if Pick Path Name already exists for this facility
				$query = "SELECT COUNT(*) as count FROM pick_configuration WHERE PickPathName = '" . mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "'";
				$q = mysqli_query($this->connectionlink, $query);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$row = mysqli_fetch_assoc($q);
				if ($row['count'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Pick Path Name already exists for this facility';
					return json_encode($json);
				}

				// Insert new configuration
				$sql = "INSERT INTO pick_configuration (FacilityID, PickPathName, parttypeid, FromDispositionID, ToDispositionID, CreatedDate, CreatedBy) VALUES ('" .
					mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "', NOW(), '" .
					$_SESSION['user']['UserId'] . "')";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$insert_id = mysqli_insert_id($this->connectionlink);

				// Save configuration details
				$this->SaveConfigurationDetails($insert_id, $configurationDetails);

				// Add tracking record
				$this->AddPickConfigurationTracking($insert_id, 'Pick Configuration Created: ' . $data['PickPathName'], 'Pick Configuration');

				$json['Success'] = true;
				$json['Result'] = "New Pick Configuration Created";
				$json['ConfigurationID'] = $insert_id;
			} else {
				// Check if Pick Path Name already exists for this facility (excluding current record)
				$query = "SELECT COUNT(*) as count FROM pick_configuration WHERE PickPathName = '" . mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "' AND ConfigurationID != '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
				$q = mysqli_query($this->connectionlink, $query);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$row = mysqli_fetch_assoc($q);
				if ($row['count'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Pick Path Name already exists for this facility';
					return json_encode($json);
				}

				// Update existing configuration
				$sql = "UPDATE pick_configuration SET FacilityID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "', PickPathName = '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "', parttypeid = '" .
					mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "', FromDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "', ToDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "', UpdatedDate = NOW(), UpdatedBy = '" .
					$_SESSION['user']['UserId'] . "' WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Update configuration details
				$this->SaveConfigurationDetails($data['ConfigurationID'], $configurationDetails);

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], 'Pick Configuration Updated: ' . $data['PickPathName'], 'Pick Configuration');

				$json['Success'] = true;
				$json['Result'] = "Pick Configuration Modified";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPickConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			$query = "SELECT ConfigurationID, FacilityID, PickPathName, parttypeid, FromDispositionID, ToDispositionID FROM pick_configuration WHERE ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				// Get configuration details
				$detailsQuery = "SELECT DetailID, parttypeid, FromDispositionID, ToDispositionID, mpn, PickQuantity, PickCompleted, Status FROM pick_configuration_details WHERE ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND Status IN ('Pending', 'Active') ORDER BY CreatedDate DESC";
				$detailsResult = mysqli_query($this->connectionlink, $detailsQuery);
				$details = [];
				if ($detailsResult && mysqli_num_rows($detailsResult) > 0) {
					while ($detailRow = mysqli_fetch_assoc($detailsResult)) {
						// Convert PickQuantity to integer for proper form binding
						$detailRow['PickQuantity'] = (int)$detailRow['PickQuantity'];
						// Convert PickCompleted to integer, default to 0 if null
						$detailRow['PickCompleted'] = (int)($detailRow['PickCompleted'] ?? 0);
						$details[] = $detailRow;
					}
				}

				$json['Success'] = true;
				$json['Result'] = $row;
				$json['Details'] = $details;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pick Configuration";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPartTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT parttypeid, parttype FROM parttype WHERE Status = '1' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "' ORDER BY parttype";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Part Types Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPickEligibleDispositions($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT disposition_id, disposition FROM disposition WHERE status = 'Active' AND PickPathEligible = 1 ORDER BY disposition";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Pick Eligible Dispositions Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAllDispositions($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT disposition_id, disposition FROM disposition WHERE status = 'Active' ORDER BY disposition";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Dispositions Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SaveConfigurationDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Get configuration details to include part type and dispositions
			$configQuery = "SELECT pc.parttypeid, pc.FromDispositionID, pc.ToDispositionID, pt.parttype FROM pick_configuration pc
							LEFT JOIN parttype pt ON pc.parttypeid = pt.parttypeid
							WHERE pc.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
			$configResult = mysqli_query($this->connectionlink, $configQuery);

			if (mysqli_num_rows($configResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid Configuration ID";
				return json_encode($json);
			}

			$configRow = mysqli_fetch_assoc($configResult);

			// Validate MPN if provided
			if (!empty($data['mpn'])) {
				// Check if MPN exists in catalog_creation table for current facility
				$mpnQuery = "SELECT mpn_id, part_type FROM catlog_creation WHERE mpn_id = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND FacilityID = '" .
					mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
				$mpnResult = mysqli_query($this->connectionlink, $mpnQuery);

				if (mysqli_num_rows($mpnResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = "Invalid MPN. MPN does not exist in the catalog for this facility.";
					return json_encode($json);
				}

				$mpnRow = mysqli_fetch_assoc($mpnResult);

				// Check if part type matches
				if (strtolower(trim($mpnRow['part_type'])) !== strtolower(trim($configRow['parttype']))) {
					$json['Success'] = false;
					$json['Result'] = "Part Type of the MPN is not matching with the Configuration Part Type. Expected: " . $configRow['parttype'] . ", Found: " . $mpnRow['part_type'];
					return json_encode($json);
				}

				// Check for duplicate MPN in the same configuration (excluding current detail if editing)
				$duplicateQuery = "SELECT DetailID FROM pick_configuration_details WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND Status IN ('Pending', 'Active')";

				if (!empty($data['DetailID'])) {
					$duplicateQuery .= " AND DetailID != '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				}

				$duplicateResult = mysqli_query($this->connectionlink, $duplicateQuery);

				if (mysqli_num_rows($duplicateResult) > 0) {
					$json['Success'] = false;
					$json['Result'] = "Duplicate MPN. This MPN is already configured for this configuration.";
					return json_encode($json);
				}
			}

			if ($data['DetailID'] == '' || !isset($data['DetailID'])) { // If New Detail
				// Insert new detail with part type and dispositions
				$sql = "INSERT INTO pick_configuration_details (ConfigurationID, parttypeid, FromDispositionID, ToDispositionID, mpn, PickQuantity, PickCompleted, CreatedDate, CreatedBy, Status) VALUES ('" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['parttypeid']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['FromDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['ToDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickQuantity']) . "', 0, NOW(), '" .
					$_SESSION['user']['UserId'] . "', 'Pending')";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$insert_id = mysqli_insert_id($this->connectionlink);

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], 'Configuration Detail Created: MPN ' . $data['mpn'], 'Pick Configuration Detail');

				$json['Success'] = true;
				$json['Result'] = "Configuration Detail Created";
				$json['DetailID'] = $insert_id;
			} else { // If Existing Detail
				// Check if detail exists and is in Pending status
				$checkSql = "SELECT Status FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				$checkResult = mysqli_query($this->connectionlink, $checkSql);

				if (mysqli_num_rows($checkResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = "Invalid Detail ID";
					return json_encode($json);
				}

				$row = mysqli_fetch_assoc($checkResult);
				if ($row['Status'] !== 'Pending') {
					$json['Success'] = false;
					$json['Result'] = "Cannot edit detail. Only Pending details can be modified.";
					return json_encode($json);
				}

				// Update existing detail with part type and dispositions
				$sql = "UPDATE pick_configuration_details SET parttypeid = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['parttypeid']) . "', FromDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['FromDispositionID']) . "', ToDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['ToDispositionID']) . "', mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "', PickQuantity = '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickQuantity']) . "', UpdatedDate = NOW(), UpdatedBy = '" .
					$_SESSION['user']['UserId'] . "' WHERE DetailID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], 'Configuration Detail Updated: MPN ' . $data['mpn'], 'Pick Configuration Detail');

				$json['Success'] = true;
				$json['Result'] = "Configuration Detail Updated";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteConfigurationDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Check if detail exists and is in Pending status
			$checkSql = "SELECT ConfigurationID, mpn, Status FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
			$checkResult = mysqli_query($this->connectionlink, $checkSql);

			if (mysqli_num_rows($checkResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid Detail ID";
				return json_encode($json);
			}

			$row = mysqli_fetch_assoc($checkResult);
			if ($row['Status'] !== 'Pending') {
				$json['Success'] = false;
				$json['Result'] = "Cannot delete detail. Only Pending details can be deleted.";
				return json_encode($json);
			}

			// Delete the detail
			$sql = "DELETE FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
			$q = mysqli_query($this->connectionlink, $sql);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Add tracking record
			$this->AddPickConfigurationTracking($row['ConfigurationID'], 'Configuration Detail Deleted: MPN ' . $row['mpn'], 'Pick Configuration Detail');

			$json['Success'] = true;
			$json['Result'] = "Configuration Detail Deleted";
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateMPN($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		try {
			// Check if MPN exists in catalog_creation table for current facility
			$mpnQuery = "SELECT cc.mpn_id, cc.part_type, pt.parttype FROM catlog_creation cc
						LEFT JOIN parttype pt ON pt.parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "'
						WHERE cc.mpn_id = '" . mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "'
						AND cc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
			$mpnResult = mysqli_query($this->connectionlink, $mpnQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($mpnResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid MPN. MPN does not exist in the catalog for this facility.";
				return json_encode($json);
			}

			$mpnRow = mysqli_fetch_assoc($mpnResult);

			// Check if part type matches
			if (strtolower(trim($mpnRow['part_type'])) !== strtolower(trim($mpnRow['parttype']))) {
				$json['Success'] = false;
				$json['Result'] = "Part Type of the MPN is not matching with the Configuration Part Type. Expected: " . $mpnRow['parttype'] . ", Found: " . $mpnRow['part_type'];
				return json_encode($json);
			}

			// Check for duplicate MPN in the same configuration
			if (!empty($data['ConfigurationID'])) {
				$duplicateQuery = "SELECT DetailID FROM pick_configuration_details WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND Status IN ('Pending', 'Active')";

				if (!empty($data['DetailID'])) {
					$duplicateQuery .= " AND DetailID != '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				}

				$duplicateResult = mysqli_query($this->connectionlink, $duplicateQuery);

				if (mysqli_num_rows($duplicateResult) > 0) {
					$json['Success'] = false;
					$json['Result'] = "Duplicate MPN. This MPN is already configured for this configuration.";
					return json_encode($json);
				}
			}

			$json['Success'] = true;
			$json['Result'] = "MPN is valid";
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	private function SaveConfigurationDetails($configurationID, $details)
	{
		// This function is no longer used as we handle details individually
		// Keeping for backward compatibility
		return true;
	}

	public function GetPickConfigurationDetailsList($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array(),
			'Total' => 0
		);

		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$page = isset($data['page']) ? (int)$data['page'] : 0;
			$itemsPerPage = isset($data['itemsPerPage']) ? (int)$data['itemsPerPage'] : 25;
			$orderBy = isset($data['OrderBy']) ? $data['OrderBy'] : 'CreatedDate';
			$orderByType = isset($data['OrderByType']) ? $data['OrderByType'] : 'desc';
			$filter = isset($data['filter']) ? $data['filter'] : array();

			// Build the base query
			$baseQuery = "FROM pick_configuration_details pcd
						  LEFT JOIN pick_configuration pc ON pcd.ConfigurationID = pc.ConfigurationID
						  LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
						  LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
						  LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
						  WHERE pc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "'
						  AND pcd.Status IN ('Pending', 'Active')";

			// Add filters
			if (!empty($filter)) {
				if (!empty($filter['PickPathName'])) {
					$baseQuery .= " AND pc.PickPathName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['PickPathName']) . "%'";
				}
				if (!empty($filter['parttype'])) {
					$baseQuery .= " AND pt.parttype LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['parttype']) . "%'";
				}
				if (!empty($filter['mpn'])) {
					$baseQuery .= " AND pcd.mpn LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['mpn']) . "%'";
				}
				if (!empty($filter['FromDisposition'])) {
					$baseQuery .= " AND fd.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['FromDisposition']) . "%'";
				}
				if (!empty($filter['ToDisposition'])) {
					$baseQuery .= " AND td.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['ToDisposition']) . "%'";
				}
				if (!empty($filter['Status'])) {
					$baseQuery .= " AND pcd.Status LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['Status']) . "%'";
				}
				if (!empty($filter['CreatedDate'])) {
					$baseQuery .= " AND DATE(pcd.CreatedDate) = '" . mysqli_real_escape_string($this->connectionlink, $filter['CreatedDate']) . "'";
				}
			}

			// Get total count
			$countQuery = "SELECT COUNT(*) as total " . $baseQuery;
			$countResult = mysqli_query($this->connectionlink, $countQuery);
			$totalRow = mysqli_fetch_assoc($countResult);
			$total = $totalRow['total'];

			// Build the main query with ordering and pagination
			$query = "SELECT pcd.DetailID, pcd.ConfigurationID, pcd.mpn, pcd.PickQuantity, pcd.PickCompleted, pcd.Status, pcd.CreatedDate,
					  pc.PickPathName, pt.parttype, fd.disposition as FromDisposition, td.disposition as ToDisposition " . $baseQuery;

			// Add ordering
			$validOrderColumns = array('PickPathName', 'parttype', 'mpn', 'PickQuantity', 'FromDisposition', 'ToDisposition', 'CreatedDate', 'Status');
			if (in_array($orderBy, $validOrderColumns)) {
				if ($orderBy == 'PickPathName') {
					$query .= " ORDER BY pc.PickPathName " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'parttype') {
					$query .= " ORDER BY pt.parttype " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'FromDisposition') {
					$query .= " ORDER BY fd.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'ToDisposition') {
					$query .= " ORDER BY td.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} else {
					$query .= " ORDER BY pcd." . $orderBy . " " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				}
			} else {
				$query .= " ORDER BY pcd.CreatedDate DESC";
			}

			// Add pagination
			$offset = $page * $itemsPerPage;
			$query .= " LIMIT " . $offset . ", " . $itemsPerPage;

			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$details = array();
			if ($result && mysqli_num_rows($result) > 0) {
				while ($row = mysqli_fetch_assoc($result)) {
					// Convert numeric fields to proper types
					$row['PickQuantity'] = (int)$row['PickQuantity'];
					$row['PickCompleted'] = (int)($row['PickCompleted'] ?? 0);
					$details[] = $row;
				}
			}

			$json['Success'] = true;
			$json['Result'] = $details;
			$json['Total'] = $total;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function ExportPickConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => $data
		);

		// Store data in session for the Excel template to use
		$_SESSION['PickConfigurationDetailsxls'] = $data;
		$json['Success'] = true;
		return json_encode($json);
	}

	private function AddPickConfigurationTracking($configurationID, $action, $moduleName)
	{
		try {
			$sql = "INSERT INTO pick_configuration_tracking (ConfigurationID, Action, CreatedDate, CreatedBy, ModuleName) VALUES ('" .
				mysqli_real_escape_string($this->connectionlink, $configurationID) . "', '" .
				mysqli_real_escape_string($this->connectionlink, $action) . "', NOW(), '" .
				$_SESSION['user']['UserId'] . "', '" .
				mysqli_real_escape_string($this->connectionlink, $moduleName) . "')";

			mysqli_query($this->connectionlink, $sql);
		} catch (Exception $e) {
			// Log error but don't fail the main operation
			error_log("Pick Configuration Tracking Error: " . $e->getMessage());
		}
	}
}

?>