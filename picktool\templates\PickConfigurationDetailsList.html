<div ng-controller="PickConfigurationDetailsList" class="page">
    <style>
        /* Status color coding */
        .pending { background-color: #ffe188; }
        .active { background-color: #b4dba4; }
        .inactive { background-color: #f0908c; }
        
        /* Status cell styling */
        .status-cell {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 500;
            min-width: 100px;
            display: inline-block;
        }
    </style>
    
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="PickDetailsList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="PickDetailsList = !PickDetailsList" class="material-icons md-primary" ng-show="PickDetailsList">keyboard_arrow_up</i>
                                <i ng-click="PickDetailsList = !PickDetailsList" class="material-icons md-primary" ng-show="!PickDetailsList">keyboard_arrow_down</i>
                                <span ng-click="PickDetailsList = !PickDetailsList">Pick Configuration Details List</span>
                                <div flex></div>

                                <a ng-click="ExportPickDetailsxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon> 
                                    <span>Export to Excel</span>
                                </a>
                                <a href="#!/PickConfiguration" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Configuration
                                </a>
                            </div>
                        </md-toolbar>
                        
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                            <p>No Pick Configuration Details available</p>
                        </div>
                        
                        <div class="row" ng-show="PickDetailsList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <th style="min-width: 120px;">Actions</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('PickPathName')" ng-class="{'orderby' : OrderBy == 'PickPathName'}">
                                                        <div>
                                                            Pick Path <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PickPathName'"></i>
                                                            <span ng-show="OrderBy == 'PickPathName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                        <div>
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                            <span ng-show="OrderBy == 'parttype'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('mpn')" ng-class="{'orderby' : OrderBy == 'mpn'}">
                                                        <div>
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'mpn'"></i>
                                                            <span ng-show="OrderBy == 'mpn'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('PickQuantity')" ng-class="{'orderby' : OrderBy == 'PickQuantity'}">
                                                        <div>
                                                            Pick Progress <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PickQuantity'"></i>
                                                            <span ng-show="OrderBy == 'PickQuantity'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FromDisposition')" ng-class="{'orderby' : OrderBy == 'FromDisposition'}">
                                                        <div>
                                                            Pick From Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FromDisposition'"></i>
                                                            <span ng-show="OrderBy == 'FromDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ToDisposition')" ng-class="{'orderby' : OrderBy == 'ToDisposition'}">
                                                        <div>
                                                            Assign Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ToDisposition'"></i>
                                                            <span ng-show="OrderBy == 'ToDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                        <div>
                                                            Pick Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                            <span ng-show="OrderBy == 'CreatedDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div>
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                </tr>
                                                
                                                <!-- Search/Filter Row -->
                                                <tr class="errornone">
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickPathName" ng-model="filter_text[0].PickPathName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="mpn" ng-model="filter_text[0].mpn" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickProgress" ng-model="filter_text[0].PickProgress" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FromDisposition" ng-model="filter_text[0].FromDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ToDisposition" ng-model="filter_text[0].ToDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="detail in pagedItems">
                                                    <td>
                                                        <div style="display: flex;">
                                                            <md-icon class="material-icons text-danger" ng-if="detail.Status === 'Pending'" 
                                                                     ng-click="editPickDetail(detail)" 
                                                                     style="cursor: pointer; font-size: 16px;"
                                                                     title="Edit">
                                                                edit
                                                            </md-icon>
                                                            <md-icon class="material-icons text-danger" ng-if="detail.Status === 'Pending'" 
                                                                     ng-click="deletePickDetail(detail.DetailID)" 
                                                                     style="cursor: pointer; font-size: 16px; margin-left: 8px;"
                                                                     title="Delete">
                                                                delete
                                                            </md-icon>
                                                            <md-icon class="material-icons text-muted" ng-if="detail.Status !== 'Pending'" 
                                                                     style="cursor: not-allowed; opacity: 0.5; font-size: 16px;"
                                                                     title="Locked - Cannot edit non-pending items">
                                                                lock
                                                            </md-icon>
                                                        </div>
                                                    </td>
                                                    <td>{{detail.PickPathName}}</td>
                                                    <td>{{detail.parttype}}</td>
                                                    <td>{{detail.mpn || 'N/A'}}</td>
                                                    <td>{{detail.PickCompleted || 0}} / {{detail.PickQuantity}}</td>
                                                    <td>{{detail.FromDisposition}}</td>
                                                    <td>{{detail.ToDisposition}}</td>
                                                    <td>{{detail.CreatedDate | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>
                                                        <span class="status-cell" ng-class="{'pending': detail.Status === 'Pending', 'active': detail.Status === 'Active', 'inactive': detail.Status === 'Inactive'}">
                                                            {{detail.Status}}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="9">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)">
                                                                    <a href>{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </md-card>
                </div>
            </article>
        </div>
    </div>
</div>
