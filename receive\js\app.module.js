(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
        'app.core'

        // Custom Feature modules
        , 'app.ui'
        , 'app.ui.form'
        , 'app.ui.form.validation'


        // 3rd party feature modules
        , 'md.data.table'
        , 'global'
        , 'angularFileUpload'
        , 'md.data.table'
        ,'ngSanitize'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
        function ($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('receive', {
                    url: '/receive',
                    templateUrl: "templates/Receive.html"
                })
                .state('newticket', {
                    url: '/newticket',
                    templateUrl: "templates/Newticket.html"
                })
                .state('newticket/:LoadId', {
                    url: '/newticket/:LoadId',
                    templateUrl: "templates/Newticket.html"
                })
                .state('Receivedemo', {
                    url: '/Receivedemo',
                    templateUrl: "templates/Receivedemo.html"
                })
                .state('upload', {
                    url: '/upload',
                    templateUrl: "templates/UploadShipment.html"
                })
                .state('upload/:UploadID', {
                    url: '/upload/:UploadID',
                    templateUrl: "templates/UploadShipment.html"
                })
                .state('PendingLoads', {
                    url: '/PendingLoads',
                    templateUrl: "templates/pendinglist.html"
                })
                .state('AssetAudit', {
                    url: '/AssetAudit',
                    templateUrl: "templates/assetaudit.html"
                })
                .state('AssetAudit/:idPallet', {
                    url: '/AssetAudit/:idPallet',
                    templateUrl: "templates/assetaudit.html"
                })
                .state('Exceptions', {
                    url: '/Exceptions',
                    templateUrl: "templates/Exceptions.html"
                })
                .state('ServerRecovery', {
                    url: '/ServerRecovery',
                    templateUrl: "templates/serverrecovery.html"
                })
                .state('ServerRecovery/:idPallet', {
                    url: '/ServerRecovery/:idPallet',
                    templateUrl: "templates/serverrecovery.html"
                })

            $urlRouterProvider
                .when('/', '/receive')
                .otherwise('/receive');
        }
    ]);

    module.controller("receive", function ($scope, $http, $filter, $upload, UserFacility, Customer, facilityinformation, ReferenceCustomer, packages, ProductClass, transportations, $rootScope, $mdToast, $mdDialog, $stateParams) {

        $scope.packageTypes = [];
        packages.async().then(function (d) { //2. so you can use .then()
            $scope.packageTypes = d['data']['Result'];
        });
        $scope.classes = [];
        ProductClass.async().then(function (d) { //2. so you can use .then()
            $scope.classes = d['data']['Result'];
        });

        $scope.categories = [];
        $scope.GetCategories = function () {

            if ($scope.newPallet.class) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCategories&classID=' + $scope.newPallet.class,
                    success: function (data) {
                        if (data.Success == true) {
                            $scope.categories = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.categories = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.categories = [];
            }

        };

        //var $scope = this;
        $scope.simulateQuery = true;
        $scope.isDisabled = false;
        // list of `state` value/display objects
        //$scope.states        = loadAll();
        $scope.querySearch = querySearch;
        $scope.selectedItemChange = selectedItemChange;
        $scope.searchTextChange = searchTextChange;


        function querySearch(query) {
            //var results = query ? $scope.states.filter( createFilterFor(query) ) : $scope.states,
            //deferred;
            if ($scope.simulateQuery) {
                if (query != '') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetLoads&keyword=' + query)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LoadId'], LoadId: res.data.Result[i]['LoadId'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }

                deferred = $q.defer();
                $timeout(function () { deferred.resolve(results); }, Math.random() * 1000, false);
                return deferred.promise;
            } else {
                return results;
            }
        }
        function searchTextChange(text) {
            $scope.SearchLoadID = text;
        }
        function selectedItemChange(item) {
            if (item) {
                if (item.value) {
                    $scope.SearchLoadID = item.value;
                } else {
                    $scope.SearchLoadID = '';
                }
            } else {
                $scope.SearchLoadID = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }
        /**
         * Build `states` list of key/value pairs
         */
        // function loadAll() {
        //     var allStates = 'Alabama, Alaska, Arizona, Arkansas, California, Colorado, Connecticut, Delaware,\
        //                     Florida, Georgia, Hawaii, Idaho, Illinois, Indiana, Iowa, Kansas, Kentucky, Louisiana,\
        //                     Maine, Maryland, Massachusetts, Michigan, Minnesota, Mississippi, Missouri, Montana,\
        //                     Nebraska, Nevada, New Hampshire, New Jersey, New Mexico, New York, North Carolina,\
        //                     North Dakota, Ohio, Oklahoma, Oregon, Pennsylvania, Rhode Island, South Carolina,\
        //                     South Dakota, Tennessee, Texas, Utah, Vermont, Virginia, Washington, West Virginia,\
        //                     Wisconsin, Wyoming';
        //     return allStates.split(/, +/g).map( function (state) {
        //         return {
        //             value: state.toLowerCase(),
        //             display: state
        //         };
        //     });
        // }
        /**
         * Create filter function for a query string
         */
        // function createFilterFor(query) {
        //     //var lowercaseQuery = angular.lowercase(query);
        //     return function filterFn(state) {
        //         //return (state.value.indexOf(lowercaseQuery) === 0);
        //         return (state.value.indexOf(query) === 0);
        //     };
        // }


        function LocationChange(text) {
            $scope.newPallet.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newPallet.location = item.value;
                } else {
                    $scope.newPallet.location = '';
                }
            } else {
                $scope.newPallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query != '') {
                return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.receive.FacilityID)
                    .then(function (res) {
                        if (res.data.Success == true) {
                            if (res.data.Result.length > 0) {
                                var result_array = [];
                                for (var i = 0; i < res.data.Result.length; i++) {
                                    result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                }
                                return result_array;
                            } else {
                                return [];
                            }
                        } else {
                            return [];
                        }
                    });
            } else {
                return [];
            }
        }





        var today = new Date();
        var adjusteddate = today.toISOString().substring(0, 10);
        $scope.receive = { 'images': [], 'Damaged': 'No', 'DateReceived': adjusteddate, 'EstimatedCompletionDate': adjusteddate, 'LotStatus': 0 };
        $scope.newPallet = { 'Splits': [], 'quantity': 1, 'weightPercent': 100 };
        $scope.pallets = [];
        Customer.async().then(function (d) { //2. so you can use .then()
            $scope.Customer = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            $scope.receive.FacilityID = $scope.UserFacility;
            //$scope.GetLocations();
            //$scope.GetLocationGroups();
        });
        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        packages.async().then(function (d) { //2. so you can use .then()
            $scope.packageTypes = d['data']['Result'];
        });
        ProductClass.async().then(function (d) { //2. so you can use .then()
            $scope.classes = d['data']['Result'];
        });
        transportations.async().then(function (d) { //2. so you can use .then()
            $scope.tranportations = d['data']['Result'];
        });
        jQuery.ajax({
            url: host + 'receive/includes/receive_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetProcesses&type=Load',
            success: function (data) {
                if (data.Success == true) {
                    $scope.processes = data.Result;
                }
                else {
                    $scope.processes = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });




        $scope.RefCustomers = [];

        $scope.GetReferenceCustomers = function () {
            if ($scope.receive.idCustomer) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReferenceCustomers&idCustomer=' + $scope.receive.idCustomer,
                    success: function (data) {
                        if (data.Success == true) {
                            $scope.RefCustomers = data.Result;
                            if ($scope.RefCustomers.length == 1) {
                                if ($scope.RefCustomers[0].Status != '2')
                                    $scope.receive.idRefCustomer = $scope.RefCustomers[0].idRefCustomer;
                            } else {
                                //$scope.receive.idRefCustomer = null;
                            }
                        }
                        else {
                            $scope.RefCustomers = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.RefCustomers = [];
            }
        };


        $scope.GetLocations = function () {
            if ($scope.receive.FacilityID != '') {
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetLocations&FacilityID=' + $scope.receive.FacilityID,
                    success: function (data) {
                        if (data.Success == true) {
                            $scope.Locations = data.Result;
                        }
                        else {
                            $scope.Locations = [];
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.LocationGroups = [];
        $scope.GetLocationGroups = function () {
            // if($scope.receive.FacilityID ) {
            //     jQuery.ajax({
            //         url: host+'pallet/includes/pallet_submit.php',
            //         dataType: 'json',
            //         type: 'post',
            //         data: 'ajax=GetLocationGroups&FacilityID='+$scope.receive.FacilityID,
            //         success: function(data){
            //             if(data.Success == true){
            //                 $scope.LocationGroups = data.Result;
            //             } else {
            //                 $scope.LocationGroups = [];
            //             }
            //             initSessionTime(); $scope.$apply();
            //         }, error : function (data) {
            //             $scope.data = data;
            //             initSessionTime(); $scope.$apply();
            //         }
            //     });
            // } else {
            //     $scope.LocationGroups = [];
            //     initSessionTime(); $scope.$apply();
            // }
        };

        $scope.GetInstructions = function () {
            if ($scope.receive.idCustomer) {
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetInstructions&idCustomer=' + $scope.receive.idCustomer,
                    success: function (data) {
                        if (data.Success == true) {
                            $scope.receive.SpecialInstructions = data.Result;
                        } else {
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
            }
        };


        $scope.CreateLoad = function () {
            $scope.receive.busy = true;

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CreateLoad&' + $.param($scope.receive) + '&' + $.param($scope.confirmDetails),
                success: function (data) {
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );

                        if (!$scope.receive.LoadId) {
                            $scope.receive.LoadId = data.LoadId;
                        }
                        $scope.receive.images = data.images;
                        $scope.receive.LotStatus = '0';
                        if ($stateParams.LoadId) {
                            $scope.SearchLoad($stateParams.LoadId);
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.receive.busy = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.receive.busy = false;
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        function PALLETEDITController($scope, $mdDialog) {
            $scope.hide = function () {
                $mdDialog.hide();
            };
            $scope.cancel = function () {
                $mdDialog.cancel();
            };
            $scope.JusthidePALLETEDITPopup = function () {
                $scope.cancel();
            };
        }

        function LoadTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };

            $scope.confirmDetails = { 'LoadCreationSecurityGuardID': '', 'LoadCreationReceivingOperatorID': '', 'LoadCreationAuditorID': '', 'LoadCreationSecurityGuardIDVerified': null, 'LoadCreationReceivingOperatorIDVerified': null, 'LoadCreationAuditorIDVerified': null };
            $scope.hideTPVRPopup = function () {
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == $scope.confirmDetails.LoadCreationReceivingOperatorID) || ($scope.confirmDetails.LoadCreationSecurityGuardID == $scope.confirmDetails.LoadCreationAuditorID)) {
                    alert('All three ID should be different');
                } else {
                    $scope.hide();
                }
            };

            $scope.JusthideTPVRPopup = function () {
                $scope.confirmDetails = { 'LoadCreationSecurityGuardID': '', 'LoadCreationReceivingOperatorID': '', 'LoadCreationAuditorID': '', 'LoadCreationSecurityGuardIDVerified': null, 'LoadCreationReceivingOperatorIDVerified': null, 'LoadCreationAuditorIDVerified': null };
                $scope.cancel();
            };


            $scope.ValidateLoadUser = function (username, field) {
                //Start check If username is verified for any other
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == username) || ($scope.confirmDetails.LoadCreationReceivingOperatorID == username && $scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) || ($scope.confirmDetails.LoadCreationAuditorID == username && $scope.confirmDetails.LoadCreationAuditorIDVerified == true)) {
                    alert('ID already verified');
                } else {
                    //End check If username is verified for any other
                    $scope.confirmDetails[field] = 'Loading';
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateLoadUser&UserName=' + username,
                        success: function (data) {
                            if (data.Success) {
                                $scope.confirmDetails[field] = true;
                            } else {
                                $scope.confirmDetails[field] = false;
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.confirmDetails[field] = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            };


            $scope.ValidateTPVRAuditor = function (username, field) {
                //Start check If username is verified for any other
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == username) || ($scope.confirmDetails.LoadCreationReceivingOperatorID == username && $scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) || ($scope.confirmDetails.LoadCreationAuditorID == username && $scope.confirmDetails.LoadCreationAuditorIDVerified == true)) {
                    alert('ID already verified');
                } else {
                    //End check If username is verified for any other
                    $scope.confirmDetails[field] = 'Loading';
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateTPVRAuditor&UserName=' + username,
                        success: function (data) {
                            if (data.Success) {
                                $scope.confirmDetails[field] = true;
                            } else {
                                $scope.confirmDetails[field] = false;
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.confirmDetails[field] = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            };

            $scope.isAllLoadVerified = function () {
                if (($scope.confirmDetails.LoadCreationAuditorIDVerified == true) && ($scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) && ($scope.confirmDetails.LoadCreationSecurityGuardID)) {
                    return false;
                } else {
                    return true;
                }
            };
        }
        $scope.confirmDetails = {};
        $scope.ShowTPVRPopup = function (ev) {
            $mdDialog.show({
                controller: LoadTPVRController,
                templateUrl: 'confirm.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    console.log(confirmDetails);
                    $scope.confirmDetails = confirmDetails;
                    if ($scope.receive.LotStatus == 0) {
                        $scope.CreateLoad();
                    }
                    if ($scope.receive.LotStatus == 1) {
                        $scope.CreateLoadFromLot();
                    }

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };

        $scope.ShowPALLETEDITPopup = function (ev) {
            $mdDialog.show({
                controller: PALLETEDITController,
                templateUrl: 'palletedit.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true
            })
        };


        $scope.searchingLoad = false;
        $scope.SearchLoad = function (SearchLoadID) {
            var loadID = '';
            if (SearchLoadID.LoadId) {
                loadID = SearchLoadID.LoadId;
            } else {
                loadID = SearchLoadID;
            }
            $scope.searchingLoad = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetLoadDetails&LoadID=' + loadID,
                success: function (data) {
                    $scope.data = data;
                    if (data.Success == true) {
                        $scope.newPallet = { 'Splits': [], 'quantity': 1, 'weightPercent': 100 };
                        $scope.receive = data.Result;
                        //$scope.receive.images = date.Images;
                        //if(!$routeParams.LoadId) {
                        $scope.pallets = data.pallets;
                        //}
                        $scope.GetLocations();
                        $scope.GetLocationGroups();
                        $scope.GetReferenceCustomers();
                        //alert(data.Result);
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.receive = { 'images': [], 'Damaged': 'No', 'DateReceived': adjusteddate, 'EstimatedCompletionDate': adjusteddate };
                    }
                    $scope.searchingLoad = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.searchingLoad = false;
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.CancelPallet = function () {
            $scope.newPallet = { 'Splits': [], 'quantity': 1, 'weightPercent': 100 };
        };


        $scope.SavingPallet = false;
        $scope.CreatePallet = function () {


            //if(($scope.newPallet.location && $scope.newPallet.Group) || (!$scope.newPallet.location && !$scope.newPallet.Group)) {
            if (false) {
            } else {
                $scope.printDetails = {};
                $scope.SavingPallet = true;
                var percentValid = true;
                //Check if Total weight % is 100
                var percent = parseFloat($scope.newPallet.weightPercent);
                for (var i = 0; i < $scope.newPallet.Splits.length; i++) {
                    percent = percent + parseFloat($scope.newPallet.Splits[i].weightPercent);
                }
                if (percent != 100) {
                    var percentValid = false;
                    $scope.SavingPallet = false;
                    //yaaaService.addAlert('','Sum of Weight(%) need to be 100',5,'danger','dir1');
                }
                //End Checking if Total weight % is 100
                if (percentValid) {
                    if ($scope.newPallet.location != '' && $scope.newPallet.location) {
                        if ($scope.newPallet.location.LocationID > 0) {
                            $scope.location = $scope.newPallet.location.LocationName;
                        } else {
                            $scope.location = $scope.newPallet.location;
                        }
                    }
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreatePallet&loadid=' + $scope.receive.LoadId + '&PalletFacilityID=' + $scope.receive.FacilityID + '&' + $.param($scope.newPallet) + '&location=' + $scope.location,
                        success: function (data) {
                            $scope.SavingPallet = false;
                            $scope.data = data;
                            if (data.Success == true) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );

                                $scope.newPallet = { 'Splits': [], 'quantity': 1, 'weightPercent': 100 };
                                //$scope.searchLocation = '';
                                $scope.SearchLoad($scope.receive.LoadId);
                                $scope.palletsForm.$setPristine();
                                $scope.palletsForm.$setUntouched();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );

                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.SavingPallet = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            }
        };

    });




    module.controller("receive_new", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload) {
        $scope.PendingTickets = [];
        //Start Pagination Logic
        $scope.itemsPerPage_pending = 10;
        $scope.currentPage_pending = 0;
        $scope.OrderBy_pending = '';
        $scope.OrderByType_pending = '';
        $scope.filter_text_pending = [{}];
        $scope.range_pending = function () {
            var rangeSize_pending = 10;
            var ret_pending = [];
            var start_pending;
            start_pending = $scope.currentPage_pending;
            if (start_pending > $scope.pageCount_pending() - rangeSize_pending) {
                start_pending = $scope.pageCount_pending() - rangeSize_pending;
            }
            for (var i = start_pending; i < start_pending + rangeSize_pending; i++) {
                ret_pending.push(i);
            }
            return ret_pending;
        };
        $scope.prevPage_pending = function () {
            if ($scope.currentPage_pending > 0) {
                $scope.currentPage_pending--;
            }
        };
        $scope.firstPage_pending = function () {
            $scope.currentPage_pending = 0;
        };
        $scope.prevPageDisabled_pending = function () {
            return $scope.currentPage_pending === 0 ? "disabled" : "";
        };
        $scope.nextPage_pending = function () {
            if ($scope.currentPage_pending < $scope.pageCount_pending() - 1) {
                $scope.currentPage_pending++;
            }
        };
        $scope.lastPage_pending = function () {
            $scope.currentPage_pending = $scope.pageCount_pending() - 1;
        };
        $scope.nextPageDisabled_pending = function () {
            return $scope.currentPage_pending === $scope.pageCount_pending() - 1 ? "disabled" : "";
        };
        $scope.pageCount_pending = function () {
            return Math.ceil($scope.total_pending / $scope.itemsPerPage_pending);
        };
        $scope.setPage_pending = function (n) {
            if (n >= 0 && n < $scope.pageCount_pending()) {
                $scope.currentPage_pending = n;
            }
        };
        $scope.CallServerFunction_pending = function (newValue) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingReceivePallets&limit=' + $scope.itemsPerPage_pending + '&skip=' + newValue * $scope.itemsPerPage_pending + '&OrderBy=' + $scope.OrderBy_pending + '&OrderByType=' + $scope.OrderByType_pending + '&' + $.param($scope.convertSingle($scope.filter_text_pending)),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.PendingTickets = data.Result;
                        if (data.total) {
                            $scope.total_pending = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.$watch("currentPage_pending", function (newValue, oldValue) {
            $scope.CallServerFunction_pending(newValue);
        });
        $scope.MakeOrderBy_pending = function (orderby) {
            $scope.OrderBy_pending = orderby;
            if ($scope.OrderByType_pending == 'asc') {
                $scope.OrderByType_pending = 'desc';
            } else {
                $scope.OrderByType_pending = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingReceivePallets&limit=' + $scope.itemsPerPage_pending + '&skip=' + $scope.currentPage_pending * $scope.itemsPerPage_pending + '&OrderBy=' + $scope.OrderBy_pending + '&OrderByType=' + $scope.OrderByType_pending + '&' + $.param($scope.convertSingle($scope.filter_text_pending)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.PendingTickets = data.Result;
                        if (data.total) {
                            $scope.total_pending = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter_pending = function () {
            if ($scope.currentPage_pending == 0) {
                $scope.CallServerFunction_pending($scope.currentPage_pending);
            } else {
                $scope.currentPage_pending = 0;
            }
        };
        //End Pagination Logic

        // $rootScope.$broadcast('preloader:active');
        // jQuery.ajax({
        //     url: host+'receive/includes/receive_submit.php',
        //     dataType: 'json',
        //     type: 'post',
        //     data: 'ajax=GetPendingReceivePallets',
        //     success: function(data){
        //         $scope.data = data;
        //         if(data.Success == true) {
        //             $scope.PendingTickets = data.Result;
        //         } else {
        //             $mdToast.show(
        //                 $mdToast.simple()
        //                     .content(data.Result)
        //                     .action('OK')
        //                     .position('right')
        //                     .hideDelay(0)
        //                     .toastClass('md-toast-danger md-block')
        //             );
        //         }
        //         $rootScope.$broadcast('preloader:hide');
        //         initSessionTime(); $scope.$apply();
        //     }, error : function (data) {
        //         $rootScope.$broadcast('preloader:hide');
        //         $scope.data = data;
        //         initSessionTime(); $scope.$apply();
        //     }
        // });


        $scope.onFileSelect = function ($files) {

            if ($("#ShipmentFile").val() != '') {
                //$files: an array of files selected, each file has name, size, and type.
                $rootScope.$broadcast('preloader:active');
                for (var i = 0; i < $files.length; i++) {
                    var file = $files[i];
                    $scope.upload = $upload.upload({
                        url: host + 'receive/includes/receive_submit.php',
                        data: { ajax: 'UploadShipmentFile' },
                        file: file, // or list of files ($files) for html5 only
                    }).success(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            location.reload();
                        } else {
                            $("#ShipmentFile").val('');
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                        // file is uploaded successfully
                    }).error(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        alert(data);
                    });
                }
            }
        };


        $scope.simulateQuery = true;
        $scope.isDisabled = false;
        $scope.querySearch = querySearch;
        $scope.selectedItemChange = selectedItemChange;
        $scope.searchTextChange = searchTextChange;


        function querySearch(query) {
            //var results = query ? $scope.states.filter( createFilterFor(query) ) : $scope.states,
            //deferred;
            if ($scope.simulateQuery) {
                if (query != '') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetLoadsNew&keyword=' + query)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LoadId'], LoadId: res.data.Result[i]['LoadId'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }

                deferred = $q.defer();
                $timeout(function () { deferred.resolve(results); }, Math.random() * 1000, false);
                return deferred.promise;
            } else {
                return results;
            }
        }
        function searchTextChange(text) {
            $scope.SearchLoadID = text;
        }
        function selectedItemChange(item) {
            if (item) {
                if (item.value) {
                    $scope.SearchLoadID = item.value;
                } else {
                    $scope.SearchLoadID = '';
                }
            } else {
                $scope.SearchLoadID = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        function LocationChange(text) {
            $scope.newPallet.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newPallet.location = item.value;
                } else {
                    $scope.newPallet.location = '';
                }
            } else {
                $scope.newPallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.receive.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        //Start Smart Search controls for Pending Container Location

        function PalletLocationChange(text, pallet) {
            pallet.location = text;
        }

        function selectedPalletLocationChange(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.location = item.value;
                } else {
                    pallet.location = '';
                }
            } else {
                pallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryPalletLocationSearch = queryPalletLocationSearch;
        $scope.PalletLocationChange = PalletLocationChange;
        $scope.selectedPalletLocationChange = selectedPalletLocationChange;
        function queryPalletLocationSearch(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }


        //End Smart Search controls for Pending Container Location



        //Start Smart Search controls for Receive Container List
        function ContainerLocationChange(text, pallet) {
            pallet.location = text;
        }

        function selectedContainerLocationChange(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.location = item.value;
                } else {
                    pallet.location = '';
                }
            } else {
                pallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryContainerLocationSearch = queryContainerLocationSearch;
        $scope.ContainerLocationChange = ContainerLocationChange;
        $scope.selectedContainerLocationChange = selectedContainerLocationChange;
        function queryContainerLocationSearch(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Smart Search controls for Receive Container List


        function LoadTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };

            $scope.confirmDetails = { 'LoadCreationSecurityGuardID': '', 'LoadCreationReceivingOperatorID': '', 'LoadCreationAuditorID': '', 'LoadCreationSecurityGuardIDVerified': null, 'LoadCreationReceivingOperatorIDVerified': null, 'LoadCreationAuditorIDVerified': null };
            $scope.hideTPVRPopup = function () {
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == $scope.confirmDetails.LoadCreationReceivingOperatorID) || ($scope.confirmDetails.LoadCreationSecurityGuardID == $scope.confirmDetails.LoadCreationAuditorID)) {
                    alert('All three ID should be different');
                } else {
                    $scope.hide();
                }
            };

            $scope.JusthideTPVRPopup = function () {
                $scope.confirmDetails = { 'LoadCreationSecurityGuardID': '', 'LoadCreationReceivingOperatorID': '', 'LoadCreationAuditorID': '', 'LoadCreationSecurityGuardIDVerified': null, 'LoadCreationReceivingOperatorIDVerified': null, 'LoadCreationAuditorIDVerified': null };
                $scope.cancel();
            };


            $scope.ValidateLoadUser = function (username, field) {
                //Start check If username is verified for any other
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == username) || ($scope.confirmDetails.LoadCreationReceivingOperatorID == username && $scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) || ($scope.confirmDetails.LoadCreationAuditorID == username && $scope.confirmDetails.LoadCreationAuditorIDVerified == true)) {
                    alert('ID already verified');
                } else {
                    //End check If username is verified for any other
                    $scope.confirmDetails[field] = 'Loading';
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateLoadUser&UserName=' + username,
                        success: function (data) {
                            if (data.Success) {
                                $scope.confirmDetails[field] = true;
                            } else {
                                $scope.confirmDetails[field] = false;
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.confirmDetails[field] = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            };


            $scope.ValidateTPVRAuditor = function (username, field) {
                //Start check If username is verified for any other
                if (($scope.confirmDetails.LoadCreationSecurityGuardID == username) || ($scope.confirmDetails.LoadCreationReceivingOperatorID == username && $scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) || ($scope.confirmDetails.LoadCreationAuditorID == username && $scope.confirmDetails.LoadCreationAuditorIDVerified == true)) {
                    alert('ID already verified');
                } else {
                    //End check If username is verified for any other
                    $scope.confirmDetails[field] = 'Loading';
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateTPVRAuditor&UserName=' + username,
                        success: function (data) {
                            if (data.Success) {
                                $scope.confirmDetails[field] = true;
                            } else {
                                $scope.confirmDetails[field] = false;
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.confirmDetails[field] = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            };

            $scope.isAllLoadVerified = function () {
                if (($scope.confirmDetails.LoadCreationAuditorIDVerified == true) && ($scope.confirmDetails.LoadCreationReceivingOperatorIDVerified == true) && ($scope.confirmDetails.LoadCreationSecurityGuardID)) {
                    return false;
                } else {
                    return true;
                }
            };
        }
        $scope.confirmDetails = {};
        $scope.ShowTPVRPopup = function (ev, pallet) {
            $mdDialog.show({
                controller: LoadTPVRController,
                templateUrl: 'confirm.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    console.log(confirmDetails);
                    $scope.confirmDetails = confirmDetails;
                    $scope.newPallet = pallet;
                    console.log($scope.newPallet);
                    $scope.ReceivePallet();

                    // if($scope.receive.LotStatus == 0) {
                    //     $scope.CreateLoad();
                    // }
                    // if($scope.receive.LotStatus == 1) {
                    //     $scope.CreateLoadFromLot();
                    // }

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };

        $scope.ShowPALLETEDITPopup = function (ev) {
            $mdDialog.show({
                controller: PALLETEDITController,
                templateUrl: 'palletedit.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true
            })
        };


        $scope.searchingLoad = false;
        $scope.SearchLoad = function (SearchLoadID) {
            var loadID = '';
            if (SearchLoadID.LoadId) {
                loadID = SearchLoadID.LoadId;
            } else {
                loadID = SearchLoadID;
            }
            $scope.searchingLoad = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetLoadDetailsNew&LoadID=' + loadID,
                success: function (data) {
                    $scope.data = data;
                    if (data.Success == true) {
                        $scope.newPallet = { 'Splits': [], 'quantity': 1, 'weightPercent': 100 };
                        $scope.receive = data.Result;
                        //$scope.receive.images = date.Images;
                        //if(!$routeParams.LoadId) {
                        $scope.pallets = data.pallets;
                        //}
                        //$scope.GetLocations();
                        //$scope.GetLocationGroups();
                        //$scope.GetReferenceCustomers();
                        //alert(data.Result);
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.receive = { 'images': [], 'Damaged': 'No' };
                    }
                    $rootScope.$broadcast('preloader:hide');
                    $scope.searchingLoad = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.searchingLoad = false;
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.SavingPallet = false;
        $scope.ReceivePallet = function () {


            //if(($scope.newPallet.location && $scope.newPallet.Group) || (!$scope.newPallet.location && !$scope.newPallet.Group)) {
            if (false) {
            } else {
                $scope.printDetails = {};
                $scope.SavingPallet = true;
                var percentValid = true;
                //Check if Total weight % is 100
                var percent = parseFloat($scope.newPallet.weightPercent);
                for (var i = 0; i < $scope.newPallet.Splits.length; i++) {
                    percent = percent + parseFloat($scope.newPallet.Splits[i].weightPercent);
                }
                if (percent != 100) {
                    var percentValid = false;
                    $scope.SavingPallet = false;
                    //yaaaService.addAlert('','Sum of Weight(%) need to be 100',5,'danger','dir1');
                }
                //End Checking if Total weight % is 100
                percentValid = true;
                if (percentValid) {
                    if ($scope.newPallet.location != '' && $scope.newPallet.location) {
                        if ($scope.newPallet.location.LocationID > 0) {
                            $scope.location = $scope.newPallet.location.LocationName;
                        } else {
                            $scope.location = $scope.newPallet.location;
                        }
                    }
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ReceivePallet&loadid=' + $scope.receive.LoadId + '&PalletFacilityID=' + $scope.receive.FacilityID + '&' + $.param($scope.newPallet) + '&location=' + $scope.location,
                        success: function (data) {
                            $scope.SavingPallet = false;
                            $scope.data = data;
                            if (data.Success == true) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                //$scope.searchLocation = '';
                                $scope.SearchLoad($scope.receive.LoadId);
                                $scope.palletsForm.$setPristine();
                                $scope.palletsForm.$setUntouched();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );

                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.SavingPallet = false;
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }
            }
        };



        $scope.ReceivePendingPallet = function (pallet) {
            pallet.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ReceivePendingPallet&' + $.param(pallet),
                success: function (data) {
                    pallet.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        for (var i = 0; i < $scope.PendingTickets.length; i++) {
                            if ($scope.PendingTickets[i]['idPallet'] == pallet.idPallet) {
                                $scope.PendingTickets.splice(i, 1);
                            }
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    initSessionTime(); $scope.$apply();
                }
            });

        };




        $scope.UpdatePallet = function (pallet) {
            pallet.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=UpdatePallet&' + $.param(pallet),
                success: function (data) {
                    pallet.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.busy = false;
        $scope.ReceiveContainerList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if ($scope.CurrentStatus != '') {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReceiveContainerList&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            console.log(data.Result);
                            $scope.pagedItems = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReceiveContainerList&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.pagedItems = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic



    });

    module.controller("manual_receive", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, facilityinformation, UserFacility, Customer, packages, $upload) {
        $scope.receive = {};
        $scope.Facility = [];
        $scope.Customer = [];
        $scope.AWSCustomers = [];
        $scope.MaterialTypes = [{ 'type': 'Component' }, { 'type': 'Loose Gear' }, { 'type': 'Decom Rack' }, { 'type': 'Media Rack' }];
        $scope.SourceTypes =[];
        $scope.AWSMaterialTypes = [];

        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host + 'receive/includes/receive_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllAWSCustomers',
            success: function (data) {
                if (data.Success == true) {
                    $scope.AWSCustomers = data.Result;
                } else {
                    $scope.AWSCustomers = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host + 'receive/includes/receive_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllAWSMaterialTypes',
            success: function (data) {
                if (data.Success == true) {
                    $scope.AWSMaterialTypes = data.Result;
                } else {
                    $scope.AWSMaterialTypes = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host + 'receive/includes/receive_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllSourceTypes',
            success: function (data) {
                if (data.Success == true) {
                    $scope.SourceTypes = data.Result;
                } else {
                    $scope.SourceTypes = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });



        Customer.async().then(function (d) { //2. so you can use .then()
            $scope.Customer = d['data']['Result'];

            // Process any pending AWS Customer ID updates
            if ($scope.pendingAWSCustomerUpdates && $scope.pendingAWSCustomerUpdates.length > 0) {
                for (var i = 0; i < $scope.pendingAWSCustomerUpdates.length; i++) {
                    $scope.UpdateAWSCustomerID1($scope.pendingAWSCustomerUpdates[i]);
                }
                // Clear the pending updates
                $scope.pendingAWSCustomerUpdates = [];
            }
        });

        $scope.packageTypes = [];
        // packages.async().then(function (d) { //2. so you can use .then()
        //     $scope.packageTypes = d['data']['Result'];
        // });


        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            $scope.receive.FacilityID = $scope.UserFacility;
            $scope.GetFacilityContainerTypes();
        });

        $scope.GetFacilityContainerTypes = function () {
            if($scope.receive.FacilityID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetFacilityContainerTypes&FacilityID=' + $scope.receive.FacilityID+'&Type=Inbound',
                    success: function (data) {
                        if (data.Success == true) {
                            $scope.packageTypes = data.Result;
                        } else {
                            $scope.packageTypes = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.receive.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.packageTypes = [];
            }
        };

        if ($stateParams.LoadId) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReceiveLoadDetails&LoadId=' + $stateParams.LoadId,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.receive = data.Result;
                        //loop through receive.Pallets and call UpdateAWSCustomerID1 function for every pallet, make sure pallets exists
                        if($scope.receive.Pallets.length > 0) {
                            for(var i=0;i<$scope.receive.Pallets.length;i++) {
                                $scope.UpdateAWSCustomerID1($scope.receive.Pallets[i]);
                            }
                        }

                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.ReceiveLoad = function () {
            $scope.receive.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ReceiveLoad&' + $.param($scope.receive),
                success: function (data) {
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );

                        if (data.NewLoad == '1') {
                            window.location = "#!/newticket/" + $scope.receive.LoadId;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.AddNewContainer = function () {
            if ($scope.receive.idLoads) {
                $scope.receive.Pallets.push({ 'idPallet': null, 'Received': 0, 'Verified': 0, 'POF': 0, 'LoadId': $scope.receive.LoadId, 'PalletFacilityID': $scope.receive.FacilityID, 'New': 1, 'SealNo2': 'n/a', 'SealNo3': 'n/a', 'SealNo4': 'n/a','WasteCustomerID': 'AWS' });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Invalid Ticket')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.RemovePallet = function (pallet, ind, ev) {
            var confirm = $mdDialog.confirm()
                .title('Would you like to delete container?')
                .content()
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('No, leave it');
            $mdDialog.show(confirm).then(function () {
                $scope.receive.Pallets.splice(ind, 1);
            }, function () {
                //$scope.status = 'You decided to keep your debt.';
            });
        };



        //Start Smart Search controls for Pending Container Location

        function PalletLocationChange(text, pallet) {
            pallet.location = text;
        }

        function selectedPalletLocationChange(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.location = item.value;
                } else {
                    pallet.location = '';
                }
            } else {
                pallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryPalletLocationSearch = queryPalletLocationSearch;
        $scope.PalletLocationChange = PalletLocationChange;
        $scope.selectedPalletLocationChange = selectedPalletLocationChange;
        function queryPalletLocationSearch(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.receive.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Smart Search controls for Pending Container Location



         //Start Smart Search controls for Pending Container Location

         function PalletLocationChange1(text, pallet) {
            pallet.group = text;
        }

        function selectedPalletLocationChange1(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.group = item.value;
                } else {
                    pallet.group = '';
                }
            } else {
                pallet.group = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryPalletLocationSearch1 = queryPalletLocationSearch1;
        $scope.PalletLocationChange1 = PalletLocationChange1;
        $scope.selectedPalletLocationChange1 = selectedPalletLocationChange1;
        function queryPalletLocationSearch1(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.receive.FacilityID+'&LocationType=Inbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Smart Search controls for Pending Container Location

        $scope.ReceiveManualPallet = function (pallet) {
            if (pallet.idPallet) {
                pallet.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ReceiveManualPallet&' + $.param(pallet),
                    success: function (data) {
                        pallet.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success == true) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            for (var i = 0; i < $scope.receive.Pallets.length; i++) {
                                if ($scope.receive.Pallets[i]['idPallet'] == pallet.idPallet) {
                                    pallet.Received = 1;
                                    if (data.LocationName) {
                                        pallet.LocationName = data.LocationName;
                                    }
                                    if(data.OnDemandMedia) {
                                        pallet.OnDemandMedia = data.OnDemandMedia;
                                    }
                                    //$scope.PendingTickets.splice(i,1);
                                }
                            }
                            if(data.Serials) {
                                alert('API Called');
                                if(data.APIERROR == '1') {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Serials)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Serials)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    ).then(function () {
                                        navigator.clipboard.writeText(JSON.stringify(data.Serials));

                                        $mdToast.show(
                                            $mdToast.simple()
                                                .content('API Response copied to clipboard')
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(1000)
                                                .toastClass('md-toast-success md-block')
                                        );
                                    });
                                }
                            }

                            if(data.Quarantine == '1') {//API failed, forced to quarantine
                                for (var i = 0; i < $scope.receive.Pallets.length; i++) {
                                    if ($scope.receive.Pallets[i]['idPallet'] == pallet.idPallet) {
                                        pallet.status = 7;
                                        pallet.LocationName = '';
                                    }
                                }

                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('API Failed, Container moved to Quarantine')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.busy = false;
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Enter Container ID')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        $scope.QuarantineManualPallet = function (pallet,ev) {
            if (pallet.idPallet) {

                var confirm = $mdDialog.confirm()
                    .title('Are you sure, you want to Quarantine Rack?')
                    .content()
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('Quarantine')
                    .cancel('Cancel');
                $mdDialog.show(confirm).then(function () {
                    pallet.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=QuarantineManualPallet&' + $.param(pallet),
                        success: function (data) {
                            pallet.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success == true) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                for (var i = 0; i < $scope.receive.Pallets.length; i++) {
                                    if ($scope.receive.Pallets[i]['idPallet'] == pallet.idPallet) {
                                        pallet.Received = 1;
                                        pallet.status = 7;
                                        if (data.LocationName) {
                                            pallet.LocationName = data.LocationName;
                                        }
                                        //$scope.PendingTickets.splice(i,1);
                                    }
                                }
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            $scope.busy = false;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }, function () {
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Enter Container ID')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        $scope.CancelTicket = function () {
            $scope.receive = {};
            window.location = "#!/newticket";
        };

        $scope.onFileSelect = function ($files) {
            if ($("#ShipmentFile").val() != '') {
                //$files: an array of files selected, each file has name, size, and type.
                $rootScope.$broadcast('preloader:active');
                for (var i = 0; i < $files.length; i++) {
                    var file = $files[i];
                    $scope.upload = $upload.upload({
                        url: host + 'receive/includes/receive_submit.php',
                        data: { ajax: 'UploadShipmentFile' },
                        file: file, // or list of files ($files) for html5 only
                    }).success(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            location.reload();
                        } else {
                            $("#ShipmentFile").val('');
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                        // file is uploaded successfully
                    }).error(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        alert(data);
                    });
                }
            }
        };

        $scope.SearchInboundLoadID = function (LoadID) {
            $scope.receive.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SearchInboundLoadID&LoadID=' + LoadID,
                success: function (data) {
                    if (data.Success == true) {
                        window.location = "#!/newticket/" + LoadID;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.NavigateToPage = function (link) {
            window.location = "#!/" + link;
        };


        $scope.GetWasteClassifctionOfContainer = function (pallet) {
            if(pallet.idPackage > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetWasteClassifctionOfContainer&idPackage=' + pallet.idPackage,
                    success: function (data) {
                        if (data.Success == true) {
                            if(data.WasteClassificationType) {
                                pallet.WasteClassificationType = data.WasteClassificationType;
                            } else {
                                pallet.WasteClassificationType = '';
                            }
                        } else {
                            pallet.WasteClassificationType = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }
        };


        $scope.UpdateAWSCustomerID = function (pallet) {
            //console.log(pallet);
            if(pallet.idCustomer > 0) {
                if($scope.Customer.length > 0) {
                    for(var i=0;i<$scope.Customer.length;i++) {
                        if($scope.Customer[i]['CustomerID'] == pallet.idCustomer) {
                            console.log('here');
                            pallet.AWSCustomerID = $scope.Customer[i]['AWSCustomerID'];
                            break;
                        }
                    }
                } else {
                    pallet.AWSCustomerID = '';
                }
            } else {
                pallet.AWSCustomerID = '';
            }
        };


        $scope.UpdateAWSCustomerID1 = function (pallet) {
            if(pallet.idCustomer > 0 && (pallet.AWSCustomerID == '' || pallet.AWSCustomerID == null || pallet.AWSCustomerID == '0')) {
                // Check if Customer data is available
                if($scope.Customer && $scope.Customer.length > 0) {
                    for(var i=0;i<$scope.Customer.length;i++) {
                        if($scope.Customer[i]['CustomerID'] == pallet.idCustomer) {
                            pallet.AWSCustomerID = $scope.Customer[i]['AWSCustomerID'];
                            break;
                        }
                    }
                } else {
                    // Customer data not yet available, set up a retry mechanism
                    if (!$scope.pendingAWSCustomerUpdates) {
                        $scope.pendingAWSCustomerUpdates = [];
                    }
                    // Store the pallet for later update
                    $scope.pendingAWSCustomerUpdates.push(pallet);
                }
            } else {
                //pallet.AWSCustomerID = '';
            }
        };

        $scope.UpdateSourceType = function (pallet) {
            if(pallet.AWSCustomerID > 0 && pallet.idCustomer > 0 && (pallet.MaterialType != '' && pallet.MaterialType)) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=GetSourceType&AWSCustomerID=' + pallet.AWSCustomerID+'&idCustomer='+pallet.idCustomer+'&MaterialType='+pallet.MaterialType,
                    data: {
                        ajax: 'GetSourceType',
                        AWSCustomerID: pallet.AWSCustomerID,
                        idCustomer: pallet.idCustomer,
                        MaterialType: pallet.MaterialType
                    },
                    success: function (data) {
                        if (data.Success == true) {
                            if(data.idCustomertype) {
                                pallet.idCustomertype = data.idCustomertype;
                            } else {
                                pallet.idCustomertype = '';
                            }
                        } else {
                            pallet.idCustomertype = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });


            } else {
                pallet.idCustomertype = '';
            }
        };

        $scope.ExportContainerListxls = function (receive) {
          //alert("1");
            jQuery.ajax({
                url: host+'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ExportContainerListxls&'+$.param(receive),
                success: function(data) {
                    if(data.Success) {
                        window.location="templates/ExportContainerListxls.php";
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                            .content(data.Result)
                            .position('right')
                            .hideDelay(3000)
                        );
                    }
                    //initSessionTime();
                    $scope.$apply();
                }, error : function (data) {
                    $scope.error = data;
                    //initSessionTime();
                    $scope.$apply();
                 }
            });
        };

    });


    module.controller("uploadShipment", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload) {
        $scope.onFileSelect = function ($files) {

            if ($("#ShipmentFile").val() != '') {
                //$files: an array of files selected, each file has name, size, and type.
                $rootScope.$broadcast('preloader:active');
                for (var i = 0; i < $files.length; i++) {
                    var file = $files[i];
                    $scope.upload = $upload.upload({
                        url: host + 'receive/includes/receive_submit.php',
                        data: { ajax: 'UploadShipmentFile' },
                        file: file, // or list of files ($files) for html5 only
                    }).success(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            if (data.UploadID) {
                                window.location = "#!/upload/" + data.UploadID;
                            }
                        } else {
                            $("#ShipmentFile").val('');
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        //initSessionTime(); $scope.$apply();
                        // file is uploaded successfully
                    }).error(function (data, status, headers, config) {
                        $rootScope.$broadcast('preloader:hide');
                        alert(data);
                    });
                }
            }
        };

        $scope.UploadDetails = [];

        if ($stateParams.UploadID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetUploadShipmentDetails&UploadID=' + $stateParams.UploadID,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.UploadDetails = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger full-width')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.receive.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }


    });


    module.controller("pendingList", function ($scope, $http, $filter, $rootScope, $mdToast, $mdDialog, $stateParams,$window) {
        $scope.pagedItems = [];
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        //$scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];

        $scope.RecordUserNavigationTransaction = function (TransactionType,Description,PageURL,id) {

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserNavigationTransaction&TransactionType=' + TransactionType + '&Description=' + Description + '&PageURL=' + PageURL+id,
                success: function (data) {

                    if (data.Success) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {
                    $scope.filter_text[0].FacilityName = data.FacilityName;
                    $scope.currentPage = 0;
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    $scope.currentPage = 0;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if ($scope.CurrentStatus != '') {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReceiveContainerList&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            console.log(data.Result);
                            $scope.pagedItems = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReceiveContainerList&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.pagedItems = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic


        //Start Smart Search controls for Receive Container List
        function ContainerLocationChange(text, pallet) {
            pallet.location = text;
        }

        function selectedContainerLocationChange(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.location = item.value;
                } else {
                    pallet.location = '';
                }
            } else {
                pallet.location = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryContainerLocationSearch = queryContainerLocationSearch;
        $scope.ContainerLocationChange = ContainerLocationChange;
        $scope.selectedContainerLocationChange = selectedContainerLocationChange;
        function queryContainerLocationSearch(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Smart Search controls for Receive Container List



        //Start Location Group Smart Search controls for Receive Container List
        function ContainerLocationChange1(text, pallet) {
            pallet.group = text;
        }

        function selectedContainerLocationChange1(item, pallet) {
            if (item) {
                if (item.value) {
                    pallet.group = item.value;
                } else {
                    pallet.group = '';
                }
            } else {
                pallet.group = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryContainerLocationSearch1 = queryContainerLocationSearch1;
        $scope.ContainerLocationChange1 = ContainerLocationChange1;
        $scope.selectedContainerLocationChange1 = selectedContainerLocationChange1;
        function queryContainerLocationSearch1(query, pallet) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID+'&LocationType=Inbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Location Group Smart Search controls for Receive Container List


        function QuarantineToActiveTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        $scope.CurrentPallet = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {
            $window.document.getElementById("AuditController").focus();
        }
        $scope.ValidateQuarantineToActiveTPVRControllerPopup = function (pallet,ev) {
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: QuarantineToActiveTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                      return pallet;
                    }
                }
            })
            .then(function(confirmDetails) {
                $rootScope.$broadcast('preloader:active');
                $scope.confirmDetails = confirmDetails;
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                    data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails),
                    success: function(data){
                        if(data.Success) {
                            $scope.confirmDetails.PasswordVerified = true;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.UpdatePallet(pallet,ev);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.confirmDetails.PasswordVerified = false;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.confirmDetails.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });
        };






        $scope.UpdatePallet = function (pallet,ev) {
            // $scope.ValidateQuarantineToActiveTPVRControllerPopup(pallet,ev);
            // return;
            pallet.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=UpdatePallet&' + $.param(pallet)+'&'+$.param($scope.confirmDetails),
                success: function (data) {
                    pallet.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        pallet.status = '1';
                        pallet.StatusValue = 'Active';
                        $scope.confirmDetails = {};
                        if(data.newLocationName) {
                            pallet.location = data.newLocationName;
                        }

                        if(data.Serials) {
                            alert('API Called');
                            if(data.APIERROR == '1') {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Serials)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Serials)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                ).then(function () {
                                    navigator.clipboard.writeText(JSON.stringify(data.Serials));

                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content('API Response copied to clipboard')
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(1000)
                                            .toastClass('md-toast-success md-block')
                                    );
                                });
                            }
                        }

                    } else {
                        if(data.Serials) {
                            alert('API Called');
                            if(data.APIERROR == '1') {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Serials)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Serials)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                ).then(function () {
                                    navigator.clipboard.writeText(JSON.stringify(data.Serials));

                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content('API Response copied to clipboard')
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(1000)
                                            .toastClass('md-toast-success md-block')
                                    );
                                });
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.confirmDetails = {};
                        if(data.TPVRRequired == '1') {
                            $scope.ValidateQuarantineToActiveTPVRControllerPopup(pallet,ev);
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    pallet.busy = false;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.PendingLoadsxls = function () {
            //alert("1");
            jQuery.ajax({
                url: host+'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratePendingLoadsxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        //console.log(data.Result);
                        window.location="templates/PendingLoadsxls.php";
                    } else {
                    // alert("4");
                    $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        function CloseContainerTPVRController($scope,$mdDialog,CurrentPallet1,$mdToast,$window) {
            $scope.CurrentPallet1 = CurrentPallet1;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails1);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails1);
            };

            $scope.FocusNextField = function (id) {
                $window.document.getElementById(id).focus();
            }
        }
        $scope.CurrentPallet1 = {};
        $scope.confirmDetails1 = {};
        function afterShowAnimation1 () {
            $window.document.getElementById("AuditController1").focus();
        }
        $scope.ValidateCloseContainerTPVRControllerPopup = function (pallet,ev) {
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: CloseContainerTPVRController,
                templateUrl: 'password1.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet1: function () {
                      return pallet;
                    }
                }
            })
            .then(function(confirmDetails1) {
                $rootScope.$broadcast('preloader:active');
                $scope.confirmDetails1 = confirmDetails1;
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                    data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails1),
                    success: function(data){
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.confirmDetails1.PasswordVerified = true;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.CloseContainer(pallet,ev);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.confirmDetails1.PasswordVerified = false;
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.confirmDetails1.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function(confirmDetails) {
                $scope.confirmDetails1 = confirmDetails;
            });
        };

        $scope.CloseContainer = function (pallet,ev) {
            pallet.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CloseContainer&' + $.param(pallet)+'&'+$.param($scope.confirmDetails1),
                success: function (data) {
                    pallet.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success == true) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.confirmDetails1 = {};
                        $scope.CallServerFunction($scope.currentPage);
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    pallet.busy = false;
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.ManageBulkRecovery = function (pallet,ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, you want to Change Bulk Recovery Rack '+pallet.idPallet+' to '+pallet.BatchRecovery)
                .content()
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                pallet.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ManageBulkRecovery&idPallet=' + pallet.idPallet+'&BatchRecovery='+pallet.BatchRecovery,
                    success: function (data) {
                        pallet.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success == true) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            //pallet.BatchRecovery = 'No';
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            //pallet.BatchRecovery = ! pallet.BatchRecovery;
                            if(pallet.BatchRecovery == 'Yes') {
                                pallet.BatchRecovery = 'No';
                            } else {
                                pallet.BatchRecovery = 'Yes';
                            }
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.busy = false;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }, function () {
                if(pallet.BatchRecovery == 'Yes') {
                    pallet.BatchRecovery = 'No';
                } else {
                    pallet.BatchRecovery = 'Yes';
                }
            });

        };

        $scope.ValidateProcessContainerID = function (pallet,ev) {
            if($scope.filter_text[0].idPallet != '' && $scope.filter_text[0].idPallet) {

                // if($scope.filter_text[0].idPallet == pallet.idPallet) {
                if(String($scope.filter_text[0].idPallet).toLowerCase() === String(pallet.idPallet).toLowerCase()) {
                    $scope.RecordUserNavigationTransaction('Receive ---> Pending Shipments','Hit on Process Button','recovery/#!/PartsRecoveryInfo/',pallet.idPallet);                    
                    window.location = "../recovery/#!/PartsRecoveryInfo/"+pallet.idPallet;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("filter_container_id").focus();    
                }
            }  else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("filter_container_id").focus();
            }
        };

    });



    module.controller("receive_serial", function ($scope, $http, $filter, $rootScope, $mdToast, $mdDialog, $stateParams, $location, $window) {
        $scope.Stations = [];
        $scope.StationCustomPallets = [];
        $scope.PalletAssets = [];
        $scope.messages = [];
        // $scope.messages = [{'message' : 'Message','show': true,'type': 'info','icon': 'info','message_type': 'Information'},
        //                 {'message' : 'Message','show': true,'type': 'danger','icon': 'error','message_type': 'Error'},
        //                 {'message' : 'Message','show': true,'type': 'success','icon': 'check_circle','message_type': 'Success'},
        //                 {'message' : 'Message','show': true,'type': 'warning','icon': 'warning','message_type': 'Warning'}];

        $scope.asset = { 'custom_id': 'n/a', 'receive_notes': 'n/a' };
        $rootScope.$broadcast('preloader:active');
        if ($stateParams.idPallet) {
            $scope.SearchPalletID = $stateParams.idPallet;
            $scope.loading = true;
            jQuery.ajax({
                url: host + 'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidatePallet&idPallet=' + $stateParams.idPallet,
                success: function (data) {
                    if (data.Success) {
                        $scope.asset.idPallet = $stateParams.idPallet;
                        if (data.LoadId) {
                            $scope.asset.LoadId = data.LoadId;
                        }
                        $scope.GetPalletAssets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.GetPalletAssets = function () {
            if ($scope.asset.idPallet) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetPalletAssets&idPallet=' + $scope.asset.idPallet,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PalletAssets = data.Result;
                        } else {
                            // $mdToast.show(
                            //     $mdToast.simple()
                            //         .content(data.Result)
                            //         .action('OK')
                            //         .position('right')
                            //         .hideDelay(0)
                            //         .toastClass('md-toast-danger md-block')
                            // );
                            $scope.PalletAssets = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }
        };

        jQuery.ajax({
            url: host + 'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=Receive Serial&workflow_id=1',
            success: function (data) {
                if (data.Success) {
                    $scope.InputResults = data.Result;
                    if (data.Default) {
                        $scope.asset.input_id = data.Default;
                    }
                } else {
                    $scope.InputResults = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host + 'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Receive Serial&workflow_id=1',
            success: function (data) {
                if (data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetStationCustomPallets = function () {
            if ($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationCustomPallets&SiteID=' + $scope.SiteID + '&Workflow=Receive Serial&workflow_id=1',
                    success: function (data) {
                        if (data.Success) {
                            $scope.StationCustomPallets = data.Result;
                        } else {
                            $scope.StationCustomPallets = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.StationCustomPallets = [];
            }
        };

        $scope.MapCustomPalletToDisposition = function (item) {
            if ($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID=' + $scope.SiteID + '&Workflow=Receive Serial&workflow_id=1' + '&' + $.param(item),
                    success: function (data) {
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.disposition_color = '';
        $scope.ApplyBusinessRule = function () {
            if ($scope.asset.UniversalModelNumber == $scope.asset.SerialNumber) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Serial Number and MPN are same')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else if ($scope.asset.input_id > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ApplyBusinessRule&workflow_id=1&' + $.param($scope.asset) + '&SiteID=' + $scope.SiteID + '&From=ReceiveSerial',
                    success: function (data) {
                        if (data.Success) {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = data.Result.disposition_id;
                            $scope.asset.disposition = data.Result.disposition;
                            $scope.asset.rule_id = data.Result.rule_id;
                            $scope.asset.rule_id_text = data.Result.rule_id_text;
                            $scope.asset.rule_description = data.Result.rule_description;
                            if (data.CustomPalletID) {
                                $scope.asset.CustomPalletID = data.CustomPalletID;
                                $scope.asset.BinName = data.BinName;
                            }
                            $scope.disposition_color = data.Result.color_code;
                            $window.document.getElementById('main_save').focus();
                            $window.document.getElementById('scan_for_save').focus();
                        } else {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = '';
                            $scope.asset.disposition = '';
                            $scope.disposition_color = '';
                            $scope.asset.CustomPalletID = '';
                            $scope.asset.BinName = '';
                            $scope.asset.rule_id = '';
                            $scope.asset.rule_id_text = '';
                            $scope.asset.rule_description = '';
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.GetMPNFromSerial = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetMPNFromSerial&SerialNumber=' + $scope.asset.SerialNumber + '&idPallet=' + $scope.asset.idPallet,
                success: function (data) {
                    if (data.Success) {
                        $scope.asset.UniversalModelNumber = data.Result.UniversalModelNumber;
                        $scope.asset.ID = data.Result.ID;
                        $scope.ApplyBusinessRule();
                    } else {
                        $scope.asset.UniversalModelNumber = '';
                        $scope.asset.ID = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.CreateAsset = function (ev) {
            if ($scope.ClosePallet) {

                var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to Close Container ?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('Close Container')
                    .cancel('Dont Close');
                $mdDialog.show(confirm).then(function () {
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'audit/includes/audit_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreateAsset&' + $.param($scope.asset) + '&SiteID=' + $scope.SiteID + '&ColsePallet=1',
                        success: function (data) {
                            if (data.Success) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                if (data.Asset) {
                                    $scope.PalletAssets.unshift(data.Asset);

                                    if ($scope.StationCustomPallets.length > 0) {
                                        for (var i = 0; i < $scope.StationCustomPallets.length; i++) {
                                            if ($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }

                                }
                                $scope.ClearAsset();
                                if (data.PalletClosed == '1') {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content('Container Closed')
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                }
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }, function () {
                    $scope.ClosePallet = false;
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'audit/includes/audit_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreateAsset&' + $.param($scope.asset) + '&SiteID=' + $scope.SiteID,
                        success: function (data) {
                            if (data.Success) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                if (data.Asset) {
                                    $scope.PalletAssets.unshift(data.Asset);

                                    if ($scope.StationCustomPallets.length > 0) {
                                        for (var i = 0; i < $scope.StationCustomPallets.length; i++) {
                                            if ($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }

                                }
                                $scope.ClearAsset();
                                $window.document.getElementById('SerialNumber').focus();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error: function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                });

            } else {
                $scope.asset.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateAsset&' + $.param($scope.asset) + '&SiteID=' + $scope.SiteID,
                    success: function (data) {
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            if (data.Asset) {
                                $scope.PalletAssets.unshift(data.Asset);

                                if ($scope.StationCustomPallets.length > 0) {
                                    for (var i = 0; i < $scope.StationCustomPallets.length; i++) {
                                        if ($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                            $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                        }
                                    }
                                }

                            }
                            $scope.ClearAsset();
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }



        };

        $scope.ClearAsset = function () {
            $scope.asset.SerialNumber = '';
            $scope.asset.UniversalModelNumber = '';
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rule_id = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';
            if ($scope.CopyCustomID) {
                //$scope.asset.custom_id = 'n/a';
            } else {
                $scope.asset.custom_id = 'n/a';
            }
            $scope.asset.receive_notes = 'n/a';
            $scope.asset.ID = '';
        };

        $scope.SerialChanged = function () {
            $scope.asset.UniversalModelNumber = '';
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rule_id = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';
            //$scope.asset.custom_id = 'n/a';
            $scope.asset.receive_notes = 'n/a';
            $scope.asset.ID = '';
        };

        $scope.MPNChanged = function () {
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rule_id = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';
            $scope.asset.custom_id = 'n/a';
            $scope.asset.receive_notes = 'n/a';
            $scope.asset.ID = '';
        };

        $scope.EditAsset = function (asset) {
            $scope.asset = asset;
        };

        $scope.SearchPallet = function (PalletID) {
            var earl = '/AssetAudit/' + PalletID;
            $location.path(earl);
        };

    });


    module.controller("ExceptionList", function ($scope, $http, $filter, $rootScope, $mdToast, $mdDialog, $stateParams) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Exceptions',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.busy = false;
        $scope.UserList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if ($scope.CurrentStatus != '') {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetExceptionList&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                    success: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.pagedItems = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            console.log(data.Result);
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetExceptionList&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.pagedItems = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        console.log(data.Result);
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic
    });


})();
