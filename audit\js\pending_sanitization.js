(function () {
    'use strict';

    angular.module('app').controller("pending_sanitization", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {        

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pending Sanitization',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $rootScope.$broadcast('preloader:active');        
        $scope.busy = false;        
        $scope.Assets = [];
        $window.document.getElementById("filter_SerialNumber").focus();
        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {            
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({                
                url: host+'audit/includes/pending_sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingSanitization&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {

                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'audit/includes/pending_sanitization_submit.php',
                dataType: 'json',
                type: 'post',                
                data: 'ajax=GetPendingSanitization&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic

        $scope.PendingMediaPannelxls = function () {
            //alert("1");
            jQuery.ajax({
                url: host+'audit/includes/pending_sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratePendingMediaPannelxls',
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        window.location="templates/PendingMediaPannelxls.php";
                    } else {
                        //alert("4");
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        
        $scope.GetHoursDifference = function (date) {

            var duration = moment.duration(moment(new Date()).diff(date));
            var days = duration.asDays();
            return days;

            var hours = duration.asHours();
            return hours;

            return moment.duration(end.diff(date)).asHours();
            return moment(date).hours();
            moment().hours()
            return date;
        };

        $scope.RecordUserNavigationTransaction = function (TransactionType,Description,PageURL,id) {

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserNavigationTransaction&TransactionType=' + TransactionType + '&Description=' + Description + '&PageURL=' + PageURL+id,
                success: function (data) {
                    
                    if (data.Success) {                        
                    } else {                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {                    
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };



        $scope.ValidateProcessSerialNumber = function (pallet,ev) {
            if($scope.filter_text[0].SerialNumber !== '' && $scope.filter_text[0].SerialNumber) {

                if($scope.filter_text[0].SerialNumber.toLowerCase() === pallet.SerialNumber.toLowerCase()) {
                    $scope.RecordUserNavigationTransaction('Services ---> Pending Sanitization','Hit on Process Button','audit/#!/Sanitization/',pallet.AssetScanID);
                    window.location = "#!/Sanitization/"+pallet.AssetScanID;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched SN is different from Processed SN')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("filter_SerialNumber").focus();
                }
            }  else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter SN in SN Filter')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("filter_SerialNumber").focus();
            }
        };


    });

    

})();