<div class="page" data-ng-controller="manual_receive">
    <div class="row ui-section">    
        <div class="col-md-12">
            <article class="article">
                <!--New ticket Start-->
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="NewticketCreationPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="NewticketCreationPanel = !NewticketCreationPanel">                            
                            <i class="material-icons md-primary" ng-show="NewticketCreationPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! NewticketCreationPanel">keyboard_arrow_down</i>
                            <span>Create New Shipment Ticket</span>
                        </div>
                    </md-toolbar>
                    
                    <div class="row" ng-show="NewticketCreationPanel">
                        <form name="ticketForm" class="form-validation">                            
                            <div class="col-md-12"> 

                                <div class="col-md-3">
                                    <md-input-container class="md-block includedsearch">
                                        <label>Ticket ID</label>
                                        <input name="LoadId" ng-model="receive.LoadId" required ng-maxlength="50" ng-minlength="3" ng-disabled="receive.idLoads" ng-enter="SearchInboundLoadID(receive.LoadId)" />
                                        <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="LoadId" ng-click="SearchInboundLoadID(receive.LoadId)" ng-disabled="!receive.LoadId">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.LoadId.$error" multiple ng-if='ticketForm.LoadId.$dirty'> 
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                            </div>
                                        </div>
                                    </md-input-container>                                    
                                </div>
                                
                                <!-- <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Source</label>
                                        <md-select name="idCustomer" ng-model="receive.idCustomer" required ng-disabled="receive.idLoads">
                                            <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.idCustomer.$error" multiple ng-if='ticketForm.idCustomer.$dirty'> 
                                                <div ng-message="required">This is required.</div>                                            
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div> -->
                            
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="receive.FacilityID" required ng-disabled="true" ng-change="GetFacilityContainerTypes()">
                                            <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>                             
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.FacilityID.$error" multiple ng-if='ticketForm.FacilityID.$dirty'>                            
                                                <div ng-message="required">This is required.</div>                                            
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Truck Seal # 1</label>
                                        <input name="SealNo" ng-model="receive.SealNo" required ng-maxlength="100">  
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.SealNo.$error" multiple ng-if='ticketForm.SealNo.$dirty'> 
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Truck Seal # 2</label>
                                        <input name="SealNo2" ng-model="receive.SealNo2" ng-maxlength="100">  
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.SealNo2.$error" multiple ng-if='ticketForm.SealNo2.$dirty'> 
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Description</label>
                                        <input name="Description" ng-model="receive.LoadDescription" ng-maxlength="1000">  
                                        <div class="error-sapce">
                                            <div ng-messages="ticketForm.LoadDescription.$error" multiple ng-if='ticketForm.LoadDescription.$dirty'> 
                                                <div ng-message="maxlength">Max length 1000.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- <div class="col-md-12 btns-row">
                                    <button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </button>
                                    <button class="md-button md-raised btn-w-md md-primary">
                                        Save
                                    </button> 
                                </div> -->

                                <div class="col-md-12 btns-row">
                                    <button class="md-button md-raised btn-w-md  md-default" ng-click="CancelTicket()">Cancel</button>

                                    <md-button 
                                    class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="ticketForm.$invalid || receive.busy" ng-click="ReceiveLoad()">
                                        <span ng-show="! receive.busy">Save</span>
                                        <span ng-show="receive.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>

                            </div>                        
                        </form>                        
                    </div>

                </md-card>
                <!--New ticket Close-->

                <!--Tickets list Start-->                
                <md-card class="no-margin-h" ng-show="receive.idLoads">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ContainersListPanel = true">
                        <div class="md-toolbar-tools">
                            <i  ng-click="ContainersListPanel = !ContainersListPanel" class="material-icons md-primary" ng-show="ContainersListPanel">keyboard_arrow_up</i>
                            <i  ng-click="ContainersListPanel = !ContainersListPanel" class="material-icons md-primary" ng-show="! ContainersListPanel">keyboard_arrow_down</i>
                            <span ng-click="ContainersListPanel = !ContainersListPanel" >Add Containers</span>
                            <div flex></div>
                            <a href="#!/newticket/{{receive.LoadId}}" ng-click="ExportContainerListxls(receive)" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                               <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon><span>Export to Excel</span>
                            </a>
                            <div class="upload-btn-wrapper text-center mt-5">
                                <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                <input type="file" ng-file-select="onFileSelect($files)" id="ShipmentFile">  
                                <a href="../../sample_files/upload_shipment.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                            </div>
                            
                            <button class="md-button md-raised btn-w-md md-primary" style="display: flex;" ng-click="AddNewContainer()">
                                <i class="material-icons">add</i> Add New Container
                            </button>

                        </div>
                    </md-toolbar>
                    
                    <md-card-content style="padding: 0px 16px;" ng-show="ContainersListPanel">
                        <div class="alert alert-warning" role="alert" ng-show="receive.Pallets.length == 0">
                            <i class="material-icons">info</i> 
                            No Containers Available
                        </div>                        
                        <md-table-container>    

                            <md-input-container class="md-block"><input type="text" name="search_text" ng-model="search_text" aria-label="text" placeholder="Search Containers"  /></md-input-container>

                            <table class="table mt-10" md-table md-row-select multiple ng-show="receive.Pallets.length > 0">
                                <thead md-head md-order="query.order">
                                    <tr md-row>
                                        <th md-column>Action</th>
                                        <th md-column style="min-width: 120px; width: 140px;">Container ID</th>
                                        <th md-column style="min-width: 120px;">Source</th>
                                        <th md-column style="min-width: 120px;">Container Type</th>
                                        <th md-column style="min-width: 72px;">Weight</th>
                                        <th md-column style="min-width: 72px;">Customer ID</th>
                                        <th md-column style="min-width: 120px;">Material Type</th>
                                        <th md-column style="min-width: 120px;">Source Type</th>
                                        <th md-column style="min-width: 110px;">Seal 1</th>
                                        <th md-column style="min-width: 110px;">Seal 2</th>
                                        <th md-column style="min-width: 110px;">Seal 3</th>
                                        <th md-column style="min-width: 110px;">Seal 4</th>
                                        <th md-column style="min-width: 110px;">Verified</th>
                                        <th md-column style="min-width: 110px;">POF</th>
                                        <th md-column style="min-width: 110px;">Classification Type</th>
                                        <th md-column style="min-width: 235px; padding-right:0px; width: 235px;">Storage Location</th>  
                                        <th md-column style="padding-right:10px; width: 60px;"></th>  
                                        <th md-column style="padding-right:0px; width: 60px;"></th>     
                                        <th md-column style="min-width: 110px;">Batch Recovery</th>                                
                                    </tr>                                    
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="pallet in receive.Pallets | filter:search_text" ng-class="{'danger' : pallet.OnDemandMedia == 'Yes'}">
                                        <td md-cell class="actionicons" style="min-width: 60px;">
                                            <a href="{{host}}label/master/examples/Receivelabel.php?id={{pallet.idPallet}}" target="_blank">
                                            <i class="material-icons print" role="img" aria-label="print">print</i>
                                            </a>
                                        </td>
                                        <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input type="text" name="idPallet" ng-model="pallet.idPallet" ng-maxlength="50" required ng-disabled="pallet.Received == 1 || pallet.New != 1">
                                            </md-input-container>
                                        </td>

                                        <td md-cell class="tdinput">
                                            <md-select name="idCustomer" ng-model="pallet.idCustomer" style="min-width:140px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required ng-change="UpdateAWSCustomerID(pallet)">
                                                <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                            </md-select>
                                        </td>

                                        <td md-cell class="tdinput">
                                            <md-select name="idPackage" ng-model="pallet.idPackage" style="min-width:140px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required ng-change="GetWasteClassifctionOfContainer(pallet);UpdateSourceType(pallet)">
                                                <md-option ng-repeat="pkg in packageTypes" value="{{pkg.idPackage}}"> {{pkg.packageName}} </md-option>                             
                                            </md-select>
                                        </td>
                                        <td md-cell class="tdinput">
                                            <md-input-container class="md-block tdinput">
                                                <input type="number" name="pallet_netweight" ng-model="pallet.pallet_netweight" required>
                                            </md-input-container>
                                        </td>

                                        <!-- <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input name="WasteCustomerID" ng-model="pallet.WasteCustomerID" maxlength="50" style="width:110px;" required>
                                            </md-input-container>
                                        </td> -->

                                        <td md-cell class="tdinput">
                                            <md-select name="AWSCustomerID" ng-model="pallet.AWSCustomerID" style="min-width:140px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required disabled>
                                                <md-option ng-repeat="cus in AWSCustomers" value="{{cus.AWSCustomerID}}"> {{cus.Customer}} </md-option>
                                            </md-select>
                                        </td>

                                        <!-- <td md-cell class="tdinput">
                                            <md-select name="MaterialType" ng-model="pallet.MaterialType" style="min-width:120px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required>
                                                <md-option ng-repeat="material in MaterialTypes" value="{{material.type}}"> {{material.type}} </md-option>                             
                                            </md-select>
                                        </td> -->

                                        <td md-cell class="tdinput">
                                            <md-select name="MaterialType" ng-model="pallet.MaterialType" style="min-width:120px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required ng-change="UpdateSourceType(pallet)">
                                                <md-option ng-repeat="material in AWSMaterialTypes" value="{{material.MaterialType}}"> {{material.MaterialType}} </md-option>                             
                                            </md-select>
                                        </td>

                                        <td md-cell class="tdinput">
                                            <md-select name="idCustomertype" ng-model="pallet.idCustomertype" style="min-width:120px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required disabled>
                                                <md-option ng-repeat="st in SourceTypes" value="{{st.idCustomertype}}"> {{st.Cumstomertype}} </md-option>
                                            </md-select>
                                        </td>

                                        <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input name="SealNo1" ng-model="pallet.SealNo1" maxlength="50" style="width:110px;" required>
                                            </md-input-container>
                                        </td>
                                        <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input name="SealNo2" ng-model="pallet.SealNo2" maxlength="50" style="width:110px;" required>
                                            </md-input-container>
                                        </td>
                                        <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input name="SealNo3" ng-model="pallet.SealNo3" maxlength="50" style="width:110px;" required>
                                            </md-input-container>
                                        </td>
                                        <td md-cell>
                                            <md-input-container class="md-block tdinput">
                                                <input name="SealNo4" ng-model="pallet.SealNo4" maxlength="50" style="width:110px;" required>
                                            </md-input-container>
                                        </td>
                                        <td md-cell>
                                            <md-switch ng-model="pallet.Verified" aria-label="Primary" class="md-default" ng-true-value="'1'" ng-false-value="'0'" ng-disabled="pallet.Received == 1"> 
                                                <span ng-show="pallet.Verified == '1'">Yes</span>
                                                <span ng-show="pallet.Verified != '1'">No</span>
                                            </md-switch>
                                        </td>
                                        <td md-cell>
                                            <md-switch ng-model="pallet.POF" aria-label="Primary" class="md-default" ng-true-value="'1'" ng-false-value="'0'" ng-disabled="pallet.Received == 1"> 
                                                <span ng-show="pallet.POF == '1'">Yes</span>
                                                <span ng-show="pallet.POF != '1'">No</span>
                                            </md-switch>
                                        </td>

                                        <td md-cell class="tdinput">
                                            <md-select name="WasteClassificationType" ng-model="pallet.WasteClassificationType" style="min-width:140px; border-bottom-width: 1px; border-color: rgba(0,0,0,0.12); border-bottom-style: solid;" required>                                                
                                                <md-option value="UEEE"> UEEE </md-option>
                                                <md-option value="WEEE"> WEEE </md-option>
                                            </md-select>
                                        </td>

                                        <!-- <td md-cell style="padding-right:0px;">                                            
                                            <span ng-show="pallet.LocationName != ''">{{pallet.LocationName}}</span>
                                            <span ng-show="pallet.status == '7'" class="text-danger">Quarantine</span>                                            
                                            <div class="autocomplete insideuse" ng-show="pallet.Received == 0">
                                                <md-autocomplete required style="width: 180px;" 
                                                    ng-disabled="pallet.Verified == 0 || pallet.POF == 0"
                                                    md-no-cache="noCache"    
                                                    md-search-text-change="PalletLocationChange(pallet.location,pallet)"      
                                                    md-search-text="pallet.location"                                  
                                                    md-items="item in queryPalletLocationSearch(pallet.location,pallet)"
                                                    md-item-text="item.LocationName"
                                                    md-selected-item-change="selectedPalletLocationChange(item,pallet)"
                                                    md-min-length="0"                                    
                                                    placeholder="Search Location">
                                                    <md-item-template>
                                                        <span md-highlight-text="pallet.location" md-highlight-flags="^i">{{item.LocationName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{pallet.location}}" were found.                                    
                                                    </md-not-found>
                                                </md-autocomplete>                                                
                                                <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-primary"  type="button" ng-disabled="(! (pallet.location && pallet.Verified == 1 && pallet.idPallet && pallet.POF == 1 && pallet.idPackage && pallet.MaterialType && pallet.pallet_netweight && pallet.SealNo1 && pallet.SealNo2 && pallet.SealNo3 && pallet.SealNo4 && pallet.idCustomer) || pallet.busy)" ng-click="ReceiveManualPallet(pallet)">
                                                    <span ng-show="! pallet.busy"><i class="material-icons" title="Receive" style="margin-top:3px;">call_received</i></span>
                                                    <span ng-show="pallet.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                </button>
                                            </div>                                            
                                        </td>  -->

                                        <td md-cell style="padding-right:0px;">                                            
                                            <span ng-show="pallet.LocationName != ''">{{pallet.LocationName}}</span>
                                            <span ng-show="pallet.status == '7'" class="text-danger">Quarantine</span>                                            
                                            <div class="autocomplete insideuse" ng-show="pallet.Received == 0">
                                                <md-autocomplete required style="width: 180px;" 
                                                    ng-disabled="pallet.Verified == 0 || pallet.POF == 0 || !pallet.AWSCustomerID || !pallet.idCustomertype"
                                                    md-no-cache="noCache"    
                                                    md-search-text-change="PalletLocationChange1(pallet.group,pallet)"      
                                                    md-search-text="pallet.group"                                  
                                                    md-items="item in queryPalletLocationSearch1(pallet.group,pallet)"
                                                    md-item-text="item.GroupName"
                                                    md-selected-item-change="selectedPalletLocationChange1(item,pallet)"
                                                    md-min-length="0"   
                                                    ng-model-options='{ debounce: 1000 }'                                 
                                                    placeholder="Search Location Group">
                                                    <md-item-template>
                                                        <span md-highlight-text="pallet.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{pallet.group}}" were found.                                    
                                                    </md-not-found>
                                                </md-autocomplete>                                                
                                                <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-primary"  type="button" ng-disabled="(! (pallet.group && pallet.Verified == 1 && pallet.idPallet && pallet.POF == 1 && pallet.idPackage && pallet.MaterialType && pallet.pallet_netweight && pallet.SealNo1 && pallet.SealNo2 && pallet.SealNo3 && pallet.SealNo4 && pallet.idCustomer) || pallet.busy)" ng-click="ReceiveManualPallet(pallet)">
                                                    <span ng-show="! pallet.busy"><i class="material-icons" title="Receive" style="margin-top:3px;">call_received</i></span>
                                                    <span ng-show="pallet.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                </button>
                                            </div>                                            
                                        </td>

                                        <td md-cell style="padding-right:10px;">                                            
                                            <button  style="min-width: 40px; min-height: 30px;" class="md-button md-raised md-accent"  type="button" ng-show="pallet.Received == 0" ng-disabled="(! (pallet.idPallet && pallet.idPackage && pallet.MaterialType && pallet.pallet_netweight && pallet.SealNo1 && pallet.SealNo2 && pallet.SealNo3 && pallet.SealNo4 && pallet.idCustomer) || pallet.busy)" ng-click="QuarantineManualPallet(pallet,$event)">
                                                <span ng-show="! pallet.busy">
                                                    <!--<i class="material-icons" title="Quarantine" style="margin-top:3px;">home</i>-->
                                                    <md-icon title="Quarantine" md-svg-src="../assets/images/quarantine24.svg" style="min-height:22px; min-width:22px;"></md-icon>
                                                </span>
                                                <span ng-show="pallet.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                            </button>                                        
                                        </td>                                       
                                        <td md-cell>
                                            <i class="material-icons text-danger" style="cursor: pointer; margin-top: 8px;" ng-show="pallet.Received == 0 && pallet.New == 1" ng-click="RemovePallet(pallet,$index,$event)"> close</i>
                                        </td>
                                        
                                        <td md-cell>
                                            <md-switch ng-model="pallet.BatchRecovery" aria-label="Primary" class="md-default" ng-true-value="'1'" ng-false-value="'0'" ng-disabled="true"></md-switch>
                                        </td>
                                    </tr>                                                                
                                </tbody>
                            </table>
                        </md-table-container>

                    </md-card-content>

                </md-card>
                <!--Tickets list Close--> 


                <div class="col-md-12 btns-row">                
                    <md-button 
                    class="md-raised btn-w-md md-primary btn-w-md"
                    data-ng-disabled="receive.busy" ng-click="NavigateToPage('PendingLoads')" ng-show="receive.idLoads">
                        <span ng-show="! receive.busy">Receive Shipment Complete</span>
                        <span ng-show="receive.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                    </md-button>
                </div>

            </article>                            
        </div>                
    </div>
</div>