ALTER TABLE `awsstg`.`disposition` ADD COLUMN `PickPathEligible` TINYINT NULL DEFAULT 0 AFTER `BulkRecoveryEditEligable`;
CREATE TABLE `awsstg`.`pick_configuration` (
  `ConfigurationID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL DEFAULT NULL,
  `PickPathName` VARCHAR(100) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ConfigurationID`));

  CREATE TABLE `awsstg`.`pick_configuration_details` (
  `DetailID` INT NOT NULL AUTO_INCREMENT,
  `ConfigurationID` INT NULL DEFAULT NULL,
  `parttypeid` INT NULL DEFAULT NULL,
  `mpn` VARCHAR(100) NULL DEFAULT NULL,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `ToDis<PERSON>ID` INT NULL DEFAULT NULL,
  `PickQuantity` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  PRIMARY KEY (`DetailID`));

  INSERT INTO `awsstg`.`tabs` (`TabID`, `TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('171', 'Pick Tool', 'picktool', 'picktool', '1', '1', '0', '22', 'local_shipping');
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Pick Configuration', 'PickConfigurationDetailsList', 'PickConfigurationDetailsList', '', '1', '', '1', '0', '1', '0');
  


  CREATE TABLE `awsstg`.`pick_configuration_tracking` (
  `TrackID` INT NOT NULL AUTO_INCREMENT,
  `ConfigurationID` INT NULL DEFAULT NULL,
  `Action` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `ModuleName` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`TrackID`));



ALTER TABLE `awsstg`.`pick_configuration` 
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `UpdatedBy`,
ADD COLUMN `FromDispositionID` INT NULL DEFAULT NULL AFTER `parttypeid`,
ADD COLUMN `ToDispositionID` INT NULL DEFAULT NULL AFTER `FromDispositionID`,
ADD COLUMN `Status` VARCHAR(10) NULL DEFAULT NULL AFTER `ToDispositionID`;

ALTER TABLE `awsstg`.`pick_configuration_details` 
ADD COLUMN `PickCompleted` INT NULL DEFAULT NULL COMMENT 'sofar completed count' AFTER `Status`;

ALTER TABLE `awsstg`.`users` ADD COLUMN `PickerController` TINYINT NULL DEFAULT 0 AFTER `TDRManagerController`;