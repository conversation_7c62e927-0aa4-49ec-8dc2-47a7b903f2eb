
<div class="row page" data-ng-controller="Disposition">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Disposition</span>
                        <div flex></div>
                            <a href="#!/DispositionList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="Disposition_form" class="form-validation" data-ng-submit="submitForm()">                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Disposition Name</label>
                                    <input type="text" name="disposition"  ng-model="disposition['disposition']"  required ng-maxlength="100" />
                                    <div class="error-space"> 
                                    <div ng-messages="Disposition_form.disposition.$error" multiple ng-if='Disposition_form.disposition.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 100.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="disposition_description"  ng-model="disposition['disposition_description']"  required ng-maxlength="250" />
                                    <div class="error-space"> 
                                    <div ng-messages="Disposition_form.disposition_description.$error" multiple ng-if='Disposition_form.disposition_description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>

                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parent Disposition</label>   
                                    <md-select name="parent_disposition_id" ng-model="disposition.parent_disposition_id" aria-label="select" ng-disabled="disposition.disposition_id">
                                        <md-option ng-repeat="Dispositionform in Disposition" value="{{Dispositionform.parent_disposition_id}}"> {{Dispositionform.parentdispositionname}} </md-option>                                    
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="Disposition_form.parent_disposition_id.$error" multiple ng-if='Disposition_form.parent_disposition_id.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>  
                                </div>   
                                </md-input-container>
                            </div> -->

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Shipment Next Step</label>
                                    <input type="text" name="NextStep"  ng-model="disposition['NextStep']"  ng-maxlength="500" />
                                    <div class="error-space"> 
                                    <div ng-messages="Disposition_form.NextStep.$error" multiple ng-if='Disposition_form.NextStep.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 500.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>

                           
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="status" ng-model="disposition.status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div ng-messages="Disposition_form.status.$error" multiple ng-if='Disposition_form.status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Color</label>
                                    <md-select name="color_code" ng-model="disposition.color_code" required aria-label="select" id="color_code">
                                        <md-option value="{{color.color_code}}" ng-repeat="color in color_codes">
                                            <span style="background-color: {{color.color_code}};">{{color.color}}</span>
                                        </md-option>
                                    </md-select>  
                                    <div class="error-space">  
                                    <div ng-messages="Disposition_form.color_code.$error" multiple ng-if='Disposition_form.color_code.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>   
                                    </div>                                          
                                </md-input-container>
                            </div>

                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Serialized</label>
                                    <md-select name="serialized " ng-model="disposition.serialized " required aria-label="select">
                                        <md-option value="Yes"> Yes </md-option>
                                        <md-option value="No"> No </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="Disposition_form.serialized .$error" multiple ng-if='Disposition_form.serialized.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Classification Type</label>
                                    <md-select name="WasteClassificationType" ng-model="disposition.WasteClassificationType" required aria-label="select">
                                        <md-option value="UEEE"> UEEE </md-option>
                                        <md-option value="WEEE"> WEEE </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="Disposition_form.WasteClassificationType.$error" multiple ng-if='Disposition_form.WasteClassificationType.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.shipping_removal_type" aria-label="Shipment Removal Type" ng-true-value="'1'" ng-false-value="'0'"> Shipment Removal Type</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.eligible_for_bin_to_container" aria-label="Eligible for Bin to Container Consolidation" ng-true-value="'1'" ng-false-value="'0'"> Eligible for Bin to Container Consolidation</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.eligible_for_bin_consolidation" aria-label="Eligible for Bin Consolidation" ng-true-value="'1'" ng-false-value="'0'"> Eligible for Bin Consolidation</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.eligible_for_disposition_override" aria-label="Eligible for Disposition Override" ng-true-value="'1'" ng-false-value="'0'"> Eligible for Disposition Override</md-switch>
                            </div>
                             <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.destruction_disposition" aria-label="Destruction Disposition" ng-true-value="'1'" ng-false-value="'0'"> Destruction Disposition</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.BulkRecoveryEditEligable" aria-label="Bulk Recovery Edit Eligible" ng-true-value="'1'" ng-false-value="'0'"> Bulk Recovery Edit Eligible</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="disposition.PickPathEligible" aria-label="Pick Path Eligible" ng-true-value="'1'" ng-false-value="'0'"> Pick Path Eligible</md-switch>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/DispositionList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="Disposition_form.$invalid || disposition.busy" ng-click="savedisposition()">
                                <span ng-show="! disposition.busy">Save</span>
                                <span ng-show="disposition.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>