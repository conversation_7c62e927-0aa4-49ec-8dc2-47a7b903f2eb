<div class="row page" data-ng-controller="PickConfiguration">
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Pick Configuration</span>
                        <div flex></div>
                           <a href="#!/PickConfigurationDetailsList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                <div class="row">
                    <div class="col-md-12">
                        

                        <form name="pick_configuration_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Facility *</label>
                                    <md-select name="FacilityID" ng-model="PickConfiguration.FacilityID" required ng-disabled="true">
                                        <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="pick_configuration_form.FacilityID.$error" multiple ng-if='pick_configuration_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Pick Path Name *</label>
                                    <input type="text" name="PickPathName" ng-model="PickConfiguration.PickPathName" required ng-maxlength="100" ng-disabled="hasConfigurationDetails" />
                                    <div class="error-space">
                                        <div ng-messages="pick_configuration_form.PickPathName.$error" multiple ng-if='pick_configuration_form.PickPathName.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Part Type *</label>
                                    <md-select name="parttypeid" ng-model="PickConfiguration.parttypeid" required ng-disabled="hasConfigurationDetails">
                                        <md-option ng-value="parttype.parttypeid" ng-repeat="parttype in PartTypes">{{parttype.parttype}}</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="pick_configuration_form.parttypeid.$error" multiple ng-if='pick_configuration_form.parttypeid.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Pick From Disposition *</label>
                                    <md-select name="FromDispositionID" ng-model="PickConfiguration.FromDispositionID" required ng-disabled="hasConfigurationDetails">
                                        <md-option ng-value="disp.disposition_id" ng-repeat="disp in PickEligibleDispositions">{{disp.disposition}}</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="pick_configuration_form.FromDispositionID.$error" multiple ng-if='pick_configuration_form.FromDispositionID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Assign Disposition *</label>
                                    <md-select name="ToDispositionID" ng-model="PickConfiguration.ToDispositionID" required ng-disabled="hasConfigurationDetails">
                                        <md-option ng-value="disp.disposition_id" ng-repeat="disp in AllDispositions">{{disp.disposition}}</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="pick_configuration_form.ToDispositionID.$error" multiple ng-if='pick_configuration_form.ToDispositionID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>



                            <div class="col-md-12 btns-row">
                                <a href="#!/PickConfigurationList" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md md-default">
                                        Cancel
                                    </md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-if="!hasConfigurationDetails"
                                    data-ng-disabled="pick_configuration_form.$invalid || PickConfiguration.busy" ng-click="PickConfigurationSave()">
                                    <span ng-show="!PickConfiguration.busy">Save</span>
                                    <span ng-show="PickConfiguration.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>
    </div>

    <!-- Configuration Details Section - Only show after configuration is created -->
    <div class="row" ng-if="PickConfiguration.ConfigurationID">
        <div class="col-md-12">
            <md-card>
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Configuration Details</span>
                        <div flex></div>
                        <md-button class="md-raised md-primary" ng-click="addConfigurationDetail()">
                            <i class="material-icons">add</i> Add Detail
                        </md-button>
                    </div>
                </md-toolbar>

                <md-card-content>
                    <!-- Add/Edit Detail Form -->
                    <div class="row" ng-if="showAddDetailForm" style="margin-top: 20px;">
                        <div class="col-md-12">
                            <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px;">
                                <h4 style="margin-top: 0; font-weight: 500;">
                                    {{isEditingDetail ? 'Edit' : 'Add'}} Configuration Detail
                                </h4>

                                <form name="detail_form" novalidate style="margin-top: 20px;">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Part Type</label>
                                                <input type="text" ng-model="DetailForm.PartTypeName" readonly style="background-color: #f5f5f5;" />
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Pick From Disposition</label>
                                                <input type="text" ng-model="DetailForm.FromDispositionName" readonly style="background-color: #f5f5f5;" />
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Assign Disposition</label>
                                                <input type="text" ng-model="DetailForm.ToDispositionName" readonly style="background-color: #f5f5f5;" />
                                            </md-input-container>
                                        </div>
                                    </div>

                                    <div class="row" style="margin-top: 15px;">
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>MPN</label>
                                                <input type="text" name="mpn" ng-model="DetailForm.mpn" placeholder="Enter MPN" ng-maxlength="100" ng-blur="validateMPN(DetailForm.mpn)" />
                                                <div class="error-space">
                                                    <div ng-messages="detail_form.mpn.$error" multiple ng-if='detail_form.mpn.$dirty'>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Pick Quantity *</label>
                                                <input type="number" name="PickQuantity" ng-model="DetailForm.PickQuantity" placeholder="Enter Quantity" min="1" required />
                                                <div class="error-space">
                                                    <div ng-messages="detail_form.PickQuantity.$error" multiple ng-if='detail_form.PickQuantity.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                        <div ng-message="min">Minimum quantity is 1.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12" style="padding-top: 30px; text-align: center; border-top: 1px solid #e9ecef; margin-top: 25px; padding-top: 25px;">
                                            <md-button class="md-raised" ng-click="cancelAddDetail()" style="margin-right: 15px; min-width: 140px; padding: 12px 24px; font-weight: 500;">
                                                <i class="material-icons" style="margin-right: 8px; font-size: 18px;">cancel</i>
                                                Cancel
                                            </md-button>
                                            <md-button class="md-raised md-primary" ng-click="saveConfigurationDetail()" ng-disabled="detail_form.$invalid || DetailForm.busy"
                                                       style="min-width: 140px; padding: 12px 24px; font-weight: 500;">
                                                <i class="material-icons" style="margin-right: 8px; font-size: 18px;">{{isEditingDetail ? 'save' : 'add'}}</i>
                                                <span ng-show="!DetailForm.busy">{{isEditingDetail ? 'Update' : 'Save'}} Detail</span>
                                                <span ng-show="DetailForm.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                                            </md-button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Details List -->
                    <div class="row" style="margin-top: 40px;">
                        <div class="col-md-12">
                            <div style="background-color: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden;">
                                <div style="background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #e9ecef;">
                                    <h5 style="margin: 0; color: #495057; font-weight: 600;">
                                        <i class="material-icons" style="vertical-align: middle; margin-right: 8px; color: #6c757d;">list</i>
                                        Configuration Details
                                    </h5>
                                </div>
                                <md-table-container>
                                    <table md-table style="background-color: #fff;">
                                    <thead md-head>
                                        <tr md-row>
                                            <th md-column>Actions</th>
                                            <th md-column>Part Type</th>
                                            <th md-column>Pick From</th>
                                            <th md-column>Assign To</th>
                                            <th md-column>MPN</th>
                                            <th md-column>Pick Progress</th>
                                            <th md-column>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="detail in ConfigurationDetails track by detail.DetailID">
                                            <td md-cell>
                                                <div style="display: flex; ">
                                                    <md-icon class="material-icons text-danger" ng-if="detail.Status === 'Pending'"
                                                             ng-click="editConfigurationDetail(detail)"
                                                             style="cursor: pointer; font-size: 16px;"
                                                             title="Edit">
                                                        edit
                                                    </md-icon>
                                                    <md-icon class="material-icons text-danger" ng-if="detail.Status === 'Pending'"
                                                             ng-click="deleteConfigurationDetail(detail.DetailID)"
                                                             style="cursor: pointer; font-size: 16px; margin-left: 8px;"
                                                             title="Delete">
                                                        delete
                                                    </md-icon>
                                                    <md-icon class="material-icons text-muted" ng-if="detail.Status !== 'Pending'"
                                                             style="cursor: not-allowed; opacity: 0.5; font-size: 16px;"
                                                             title="Locked - Cannot edit non-pending items">
                                                        lock
                                                    </md-icon>
                                                </div>
                                            </td>
                                            <td md-cell>{{getPartTypeName(PickConfiguration.parttypeid)}}</td>
                                            <td md-cell>{{getDispositionName(PickConfiguration.FromDispositionID, 'PickEligible')}}</td>
                                            <td md-cell>{{getDispositionName(PickConfiguration.ToDispositionID, 'All')}}</td>
                                            <td md-cell>{{detail.mpn || 'N/A'}}</td>
                                            <td md-cell>{{detail.PickCompleted || 0}} / {{detail.PickQuantity}}</td>
                                            <td md-cell>
                                                <span class="label" ng-class="{'label-warning': detail.Status === 'Pending', 'label-success': detail.Status === 'Active', 'label-danger': detail.Status === 'Inactive'}">
                                                    {{detail.Status}}
                                                </span>
                                            </td>
                                        </tr>
                                        <!-- Show message when no details exist -->
                                        <tr md-row ng-if="ConfigurationDetails.length === 0">
                                            <td md-cell colspan="7" style="text-align: center; padding: 60px 40px; background-color: #f8f9fa;">
                                                <div style="color: #6c757d;">
                                                    <i class="material-icons" style="font-size: 48px; color: #dee2e6; margin-bottom: 15px;">inventory_2</i>
                                                    <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">No Configuration Details</div>
                                                    <div style="font-size: 14px;">Click "Add Detail" to start adding MPN and quantity information.</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                    </table>
                                </md-table-container>
                            </div>
                        </div>
                    </div>
                </md-card-content>
            </md-card>
        </div>
    </div>
</div>