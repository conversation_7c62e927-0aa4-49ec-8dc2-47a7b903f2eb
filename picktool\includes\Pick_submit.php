<?php
session_start();
include_once("../database/Pick.class.php");
$obj = new PickClass();

if($_POST['ajax'] == "PickConfigurationSave"){
	$result = $obj->PickConfigurationSave($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickConfigurationDetails"){
	$result = $obj->GetPickConfigurationDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPartTypes"){
	$result = $obj->GetPartTypes($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickEligibleDispositions"){
	$result = $obj->GetPickEligibleDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetAllDispositions"){
	$result = $obj->GetAllDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "SaveConfigurationDetail"){
	$result = $obj->SaveConfigurationDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "DeleteConfigurationDetail"){
	$result = $obj->DeleteConfigurationDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "ValidateMPN"){
	$result = $obj->ValidateMPN($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickConfigurationDetailsList"){
	$result = $obj->GetPickConfigurationDetailsList($_POST);
	echo $result;
}

if($_POST['ajax'] == "ExportPickConfigurationDetails"){
	$result = $obj->ExportPickConfigurationDetails($_POST);
	echo $result;
}

?>