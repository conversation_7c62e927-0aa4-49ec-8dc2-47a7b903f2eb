(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
         'app.core'

        // Custom Feature modules
        ,'app.ui'
        ,'app.ui.form'
        ,'app.ui.form.validation'


        // 3rd party feature modules
        ,'md.data.table'
        ,'global'
        ,'angularFileUpload'
        ,'angularMoment'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
        function($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('PickConfiguration', {
                    url: '/PickConfiguration',
                    templateUrl: "templates/PickConfiguration.html"
                })
                .state('PickConfiguration/:ConfigurationID', {
                    url: '/PickConfiguration/:ConfigurationID',
                    templateUrl: "templates/PickConfiguration.html"
                })                
                .state('PickConfigurationDetailsList', {
                    url: '/PickConfigurationDetailsList',
                    templateUrl: "templates/PickConfigurationDetailsList.html"
                })
                .state('PickPath', {
                    url: '/PickPath',
                    templateUrl: "templates/PickPath.html"
                })

            $urlRouterProvider
                .when('/', '/PickConfiguration')
                .otherwise('/PickConfiguration');
        }
    ]);


    module.controller("PickConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,UserFacility) {
        $rootScope.$broadcast('preloader:active');

        // Check page permissions
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pick Configuration',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize scope variables
        $scope.PickConfiguration = {};
        $scope.Facilities = [];
        $scope.PartTypes = [];
        $scope.PickEligibleDispositions = [];
        $scope.AllDispositions = [];
        $scope.ConfigurationDetails = [];
        $scope.DetailForm = {};
        $scope.showAddDetailForm = false;
        $scope.isEditingDetail = false;
        $scope.hasConfigurationDetails = false;

        // Get user facility and set as default
        UserFacility.async().then(function (d) {
            $scope.UserFacility = d.data;
            $scope.PickConfiguration.FacilityID = $scope.UserFacility;
        });

        // Load facilities
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Part Types for current facility
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypes',
            success: function (data) {
                if (data.Success) {
                    $scope.PartTypes = data.Result;
                } else {
                    $scope.PartTypes = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Pick Eligible Dispositions (PickPathEligible = 1)
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPickEligibleDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.PickEligibleDispositions = data.Result;
                } else {
                    $scope.PickEligibleDispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Load All Dispositions
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.AllDispositions = data.Result;
                } else {
                    $scope.AllDispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Configuration Details Functions
        $scope.addConfigurationDetail = function() {
            $scope.DetailForm = {
                PartTypeName: $scope.getPartTypeName($scope.PickConfiguration.parttypeid),
                FromDispositionName: $scope.getDispositionName($scope.PickConfiguration.FromDispositionID, 'PickEligible'),
                ToDispositionName: $scope.getDispositionName($scope.PickConfiguration.ToDispositionID, 'All'),
                mpn: '',
                PickQuantity: '',
                busy: false
            };
            $scope.isEditingDetail = false;
            $scope.showAddDetailForm = true;
        };

        $scope.editConfigurationDetail = function(detail) {
            $scope.DetailForm = {
                DetailID: detail.DetailID,
                PartTypeName: $scope.getPartTypeName($scope.PickConfiguration.parttypeid),
                FromDispositionName: $scope.getDispositionName($scope.PickConfiguration.FromDispositionID, 'PickEligible'),
                ToDispositionName: $scope.getDispositionName($scope.PickConfiguration.ToDispositionID, 'All'),
                mpn: detail.mpn,
                PickQuantity: detail.PickQuantity,
                busy: false
            };
            $scope.isEditingDetail = true;
            $scope.showAddDetailForm = true;
        };

        // Validate MPN function
        $scope.validateMPN = function(mpn) {
            if (!mpn || mpn.trim() === '') {
                return; // Allow empty MPN
            }

            var validationData = {
                mpn: mpn,
                ConfigurationID: $scope.PickConfiguration.ConfigurationID,
                parttypeid: $scope.PickConfiguration.parttypeid,
                DetailID: $scope.DetailForm.DetailID || ''
            };

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateMPN&' + $.param(validationData),
                success: function (data) {
                    if (!data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(4000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error validating MPN')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.saveConfigurationDetail = function() {
            $scope.DetailForm.busy = true;

            var saveData = {
                ConfigurationID: $scope.PickConfiguration.ConfigurationID,
                DetailID: $scope.DetailForm.DetailID || '',
                mpn: $scope.DetailForm.mpn || '',
                PickQuantity: $scope.DetailForm.PickQuantity
            };

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveConfigurationDetail&' + $.param(saveData),
                success: function (data) {
                    $scope.DetailForm.busy = false;
                    if (data.Success) {
                        $scope.DetailForm = {};
                        $scope.showAddDetailForm = false;
                        $scope.isEditingDetail = false;
                        $scope.loadConfigurationDetails($scope.PickConfiguration.ConfigurationID);
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.DetailForm.busy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error saving detail')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.deleteConfigurationDetail = function(detailID) {
            if (confirm('Are you sure you want to delete this configuration detail?')) {
                jQuery.ajax({
                    url: host+'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteConfigurationDetail&DetailID=' + detailID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.loadConfigurationDetails($scope.PickConfiguration.ConfigurationID);
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-success md-block')
                            );
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error deleting detail')
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.cancelAddDetail = function() {
            $scope.DetailForm = {};
            $scope.showAddDetailForm = false;
            $scope.isEditingDetail = false;
        };

        // Helper functions to get names for display
        $scope.getPartTypeName = function(parttypeid) {
            var partType = $scope.PartTypes.find(function(pt) { return pt.parttypeid == parttypeid; });
            return partType ? partType.parttype : '';
        };

        $scope.getDispositionName = function(dispositionId, type) {
            var dispositions = type === 'PickEligible' ? $scope.PickEligibleDispositions : $scope.AllDispositions;
            var disposition = dispositions.find(function(d) { return d.disposition_id == dispositionId; });
            return disposition ? disposition.disposition : '';
        };

        // Save Pick Configuration
        $scope.PickConfigurationSave = function () {
            $scope.PickConfiguration.busy = true;
            $rootScope.$broadcast('preloader:active');

            // Prepare data with configuration details
            var saveData = angular.copy($scope.PickConfiguration);
            saveData.ConfigurationDetails = $scope.ConfigurationDetails;

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=PickConfigurationSave&' + $.param(saveData),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.PickConfiguration.busy = false;
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/PickConfigurationList";
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.PickConfiguration.busy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error saving configuration')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Load configuration details
        $scope.loadConfigurationDetails = function(configurationID) {
            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPickConfigurationDetails&ConfigurationID=' + configurationID,
                success: function (data) {
                    if (data.Success && data.Details) {
                        $scope.ConfigurationDetails = data.Details;
                        $scope.hasConfigurationDetails = data.Details.length > 0;
                        console.log('Configuration Details loaded:', data.Details);
                    } else {
                        $scope.ConfigurationDetails = [];
                        $scope.hasConfigurationDetails = false;
                        console.log('No configuration details found');
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.ConfigurationDetails = [];
                    $scope.hasConfigurationDetails = false;
                    console.log('Error loading configuration details');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Load existing configuration if ConfigurationID is provided
        if ($stateParams.ConfigurationID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPickConfigurationDetails&ConfigurationID=' + $stateParams.ConfigurationID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if (data.Success) {
                        $scope.PickConfiguration = data.Result;
                        // Load configuration details
                        $scope.loadConfigurationDetails($stateParams.ConfigurationID);
                    } else {
                        $scope.PickConfiguration = {};
                        var op = data.Result.split(' ');
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    });

    // Pick Configuration Details List Controller
    module.controller('PickConfigurationDetailsList', function($scope, $rootScope, $stateParams, $mdToast, $mdDialog) {

        // Initialize variables
        $scope.pagedItems = [];
        $scope.currentPage = 0;
        $scope.itemsPerPage = 25;
        $scope.total = 0;
        $scope.OrderBy = 'CreatedDate';
        $scope.OrderByType = 'desc';
        $scope.filter_text = [{}];
        $scope.busy = false;

        // Pagination functions
        $scope.range = function() {
            var rangeSize = 5;
            var ps = [];
            var start;

            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize+1;
            }

            for (var i=start; i<start+rangeSize; i++) {
                if (i>=0) {
                    ps.push(i);
                }
            }
            return ps;
        };

        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };

        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };

        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage)-1;
        };

        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount()) {
                $scope.currentPage++;
            }
        };

        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() ? "disabled" : "";
        };

        $scope.setPage = function(n) {
            $scope.currentPage = n;
        };

        $scope.firstPage = function() {
            $scope.currentPage = 0;
        };

        $scope.lastPage = function() {
            $scope.currentPage = $scope.pageCount();
        };

        // Watch for page changes
        $scope.$watch('currentPage', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                $scope.CallServerFunction(newValue);
            }
        });

        // Sorting function
        $scope.MakeOrderBy = function(orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.CallServerFunction($scope.currentPage);
        };

        // Filter function
        $scope.MakeFilter = function() {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        // Main server call function
        $scope.CallServerFunction = function(page) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            var data = {
                ajax: 'GetPickConfigurationDetailsList',
                page: page,
                itemsPerPage: $scope.itemsPerPage,
                OrderBy: $scope.OrderBy,
                OrderByType: $scope.OrderByType,
                filter: $scope.filter_text[0]
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: data,
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;

                    if (response.Success) {
                        $scope.pagedItems = response.Result || [];
                        $scope.total = response.Total || 0;
                    } else {
                        $scope.pagedItems = [];
                        $scope.total = 0;
                        $scope.showToast('Error loading data: ' + response.Result, 'md-toast-danger');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.pagedItems = [];
                    $scope.total = 0;
                    $scope.showToast('Error loading data: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Export to Excel function
        $scope.ExportPickDetailsxls = function() {
            var filterData = {};
            if ($scope.filter_text && $scope.filter_text[0]) {
                filterData = $scope.filter_text[0];
            }

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'ExportPickConfigurationDetails',
                    OrderBy: $scope.OrderBy || 'CreatedDate',
                    OrderByType: $scope.OrderByType || 'desc',
                    filter: filterData
                },
                success: function(response) {
                    if (response.Success) {
                        window.location = "templates/PickConfigurationDetailsxls.php";
                    } else {
                        $scope.showToast('Export failed: ' + response.Result, 'md-toast-danger');
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.showToast('Export failed: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Edit pick detail function
        $scope.editPickDetail = function(detail) {
            window.location.href = '#!/PickConfiguration/' + detail.ConfigurationID;
        };

        // Delete pick detail function
        $scope.deletePickDetail = function(detailID) {
            var confirm = $mdDialog.confirm()
                .title('Delete Pick Configuration Detail')
                .textContent('Are you sure you want to delete this pick configuration detail?')
                .ariaLabel('Delete confirmation')
                .ok('Delete')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                jQuery.ajax({
                    url: host + 'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'DeleteConfigurationDetail',
                        DetailID: detailID
                    },
                    success: function(response) {
                        if (response.Success) {
                            $scope.showToast('Pick configuration detail deleted successfully', 'md-toast-success');
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            $scope.showToast('Delete failed: ' + response.Result, 'md-toast-danger');
                        }
                        initSessionTime();
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        $scope.showToast('Delete failed: ' + error, 'md-toast-danger');
                        initSessionTime();
                        $scope.$apply();
                    }
                });
            });
        };

        // Toast message function
        $scope.showToast = function(message, toastClass) {
            $mdToast.show({
                template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                hideDelay: 3000,
                position: 'right'
            });
        };

        // Convert filter array to single object (same as TruckList)
        $scope.convertSingle = function(multiarray) {
            var result = {};
            for (var i in multiarray) {
                result[i] = multiarray[i];
            }
            return result;
        };

        // Initialize the page
        $scope.CallServerFunction(0);

    });

})();
